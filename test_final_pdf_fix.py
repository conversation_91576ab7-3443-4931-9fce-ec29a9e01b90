#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试最终的PDF签名修复
"""

import base64
import io
import os
import tempfile

def create_test_pdf_with_signature():
    """创建测试PDF并添加签名 - 模拟应用程序的完整流程"""
    print("=== 测试完整PDF签名流程 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PyPDF2 import PdfReader, PdfWriter
        from PIL import Image, ImageDraw
        
        # 1. 创建原始PDF文件（模拟用户上传的PDF）
        print("1. 创建原始PDF文件...")
        original_pdf_path = 'original_document.pdf'
        
        c = canvas.Canvas(original_pdf_path, pagesize=A4)
        c.drawString(100, 750, "合同文档")
        c.drawString(100, 700, "甲方：公司A")
        c.drawString(100, 650, "乙方：公司B")
        c.drawString(100, 600, "合同内容...")
        c.drawString(100, 400, "签名区域：")
        c.rect(100, 300, 200, 80, stroke=1, fill=0)
        c.drawString(110, 340, "请在此处签名")
        c.save()
        
        print(f"   原始PDF已创建: {original_pdf_path}")
        
        # 2. 创建签名数据（模拟用户手写签名）
        print("2. 创建签名数据...")
        
        # 创建签名图片
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "张三", fill='black')
        draw.line([(20, 60), (180, 60)], fill='black', width=2)
        
        # 转换为Base64（模拟前端发送的数据）
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        signature_base64 = base64.b64encode(img_buffer.getvalue()).decode()
        signature_data = f"data:image/png;base64,{signature_base64}"
        
        # 模拟签名位置信息（x,y,width,height）
        signature_position = "120,320,160,60"  # 在签名框内
        
        print(f"   签名数据长度: {len(signature_data)}")
        print(f"   签名位置: {signature_position}")
        
        # 3. 处理签名数据（模拟应用程序逻辑）
        print("3. 处理签名数据...")
        
        # 解析位置
        pos_parts = signature_position.split(',')
        position = {
            'x': float(pos_parts[0]),
            'y': float(pos_parts[1]),
            'width': float(pos_parts[2]),
            'height': float(pos_parts[3])
        }
        
        # 解析Base64数据
        if signature_data.startswith('data:image/'):
            clean_data = signature_data.split(',')[1]
        else:
            clean_data = signature_data
        
        image_data = base64.b64decode(clean_data)
        print(f"   解码后图片数据: {len(image_data)} bytes")
        
        # 4. 创建带签名的PDF
        print("4. 创建带签名的PDF...")
        
        # 读取原始PDF
        with open(original_pdf_path, 'rb') as f:
            reader = PdfReader(f)
            writer = PdfWriter()
            
            # 获取页面尺寸
            first_page = reader.pages[0]
            page_width = float(first_page.mediabox.width)
            page_height = float(first_page.mediabox.height)
            
            print(f"   PDF页面尺寸: {page_width} x {page_height}")
            
            # 创建签名覆盖层
            overlay_buffer = io.BytesIO()
            c2 = canvas.Canvas(overlay_buffer, pagesize=(page_width, page_height))
            
            # 创建临时签名文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_file.write(image_data)
            temp_file.close()
            
            # 转换坐标（Web坐标 -> PDF坐标）
            pdf_x = position['x']
            pdf_y = page_height - position['y'] - position['height']
            
            print(f"   坐标转换: Web({position['x']}, {position['y']}) -> PDF({pdf_x}, {pdf_y})")
            
            # 在覆盖层上绘制签名
            c2.drawImage(
                temp_file.name,
                pdf_x, pdf_y,
                width=position['width'],
                height=position['height']
            )
            
            c2.save()
            overlay_buffer.seek(0)
            
            # 清理临时文件
            os.remove(temp_file.name)
            
            # 读取覆盖层
            overlay_reader = PdfReader(overlay_buffer)
            
            # 合并页面
            for page_num, page in enumerate(reader.pages):
                if page_num == 0 and len(overlay_reader.pages) > 0:
                    # 第一页添加签名
                    page.merge_page(overlay_reader.pages[0])
                writer.add_page(page)
            
            # 保存最终PDF
            signed_pdf_path = 'signed_document.pdf'
            with open(signed_pdf_path, 'wb') as output_file:
                writer.write(output_file)
        
        print(f"✅ 带签名PDF创建成功: {signed_pdf_path}")
        
        # 5. 验证结果
        print("5. 验证结果...")
        
        # 检查文件大小
        original_size = os.path.getsize(original_pdf_path)
        signed_size = os.path.getsize(signed_pdf_path)
        
        print(f"   原始PDF大小: {original_size} bytes")
        print(f"   签名PDF大小: {signed_size} bytes")
        
        if signed_size > original_size:
            print("✅ 签名PDF文件更大，可能包含签名数据")
        else:
            print("⚠️  签名PDF文件大小异常")
        
        # 清理原始文件
        os.remove(original_pdf_path)
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_multiple_signatures():
    """测试多个签名"""
    print("\n=== 测试多个签名 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PyPDF2 import PdfReader, PdfWriter
        from PIL import Image, ImageDraw
        
        # 创建原始PDF
        original_path = 'multi_original.pdf'
        c = canvas.Canvas(original_path, pagesize=A4)
        c.drawString(100, 750, "多签名测试文档")
        c.drawString(100, 600, "签名区域1:")
        c.rect(100, 500, 150, 60, stroke=1, fill=0)
        c.drawString(100, 400, "签名区域2:")
        c.rect(300, 500, 150, 60, stroke=1, fill=0)
        c.save()
        
        # 创建多个签名
        signatures = []
        positions = [
            (110, 510, 130, 40),  # 第一个签名
            (310, 510, 130, 40),  # 第二个签名
        ]
        names = ["张三", "李四"]
        
        for i, (name, pos) in enumerate(zip(names, positions)):
            # 创建签名图片
            img = Image.new('RGB', (130, 40), 'white')
            draw = ImageDraw.Draw(img)
            draw.text((10, 10), name, fill='black')
            draw.line([(10, 30), (120, 30)], fill='black', width=1)
            
            # 转换为Base64
            img_buffer = io.BytesIO()
            img.save(img_buffer, format='PNG')
            signature_data = base64.b64encode(img_buffer.getvalue()).decode()
            
            signatures.append({
                'data': f"data:image/png;base64,{signature_data}",
                'position': f"{pos[0]},{pos[1]},{pos[2]},{pos[3]}",
                'name': name
            })
        
        # 处理PDF
        with open(original_path, 'rb') as f:
            reader = PdfReader(f)
            writer = PdfWriter()
            
            page_width, page_height = A4
            
            # 创建覆盖层
            overlay_buffer = io.BytesIO()
            c2 = canvas.Canvas(overlay_buffer, pagesize=A4)
            
            temp_files = []
            
            for i, sig in enumerate(signatures):
                # 解析数据
                clean_data = sig['data'].split(',')[1]
                image_data = base64.b64decode(clean_data)
                
                pos_parts = sig['position'].split(',')
                x, y, w, h = map(float, pos_parts)
                
                # 创建临时文件
                temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
                temp_file.write(image_data)
                temp_file.close()
                temp_files.append(temp_file.name)
                
                # 转换坐标
                pdf_x = x
                pdf_y = page_height - y - h
                
                # 绘制签名
                c2.drawImage(temp_file.name, pdf_x, pdf_y, width=w, height=h)
                
                print(f"   添加签名 {i+1}: {sig['name']} 位置({pdf_x}, {pdf_y})")
            
            c2.save()
            overlay_buffer.seek(0)
            
            # 清理临时文件
            for temp_file in temp_files:
                os.remove(temp_file)
            
            # 合并
            overlay_reader = PdfReader(overlay_buffer)
            
            for page_num, page in enumerate(reader.pages):
                if page_num == 0:
                    page.merge_page(overlay_reader.pages[0])
                writer.add_page(page)
            
            # 保存
            with open('multi_signed.pdf', 'wb') as output_file:
                writer.write(output_file)
        
        # 清理
        os.remove(original_path)
        
        print("✅ 多签名测试成功: multi_signed.pdf")
        return True
        
    except Exception as e:
        print(f"❌ 多签名测试失败: {e}")
        return False

def main():
    """主函数"""
    print("=== PDF签名修复测试 ===")
    
    # 检查依赖
    try:
        import reportlab
        import PyPDF2
        from PIL import Image
        print("✅ 所有依赖库可用")
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        return
    
    # 运行测试
    test1 = create_test_pdf_with_signature()
    test2 = test_multiple_signatures()
    
    print(f"\n=== 测试结果 ===")
    if test1 and test2:
        print("✅ 所有测试通过！")
        print("\n生成的文件:")
        print("- signed_document.pdf: 单签名测试")
        print("- multi_signed.pdf: 多签名测试")
        print("\n请打开这些PDF文件检查签名是否正确显示。")
        print("如果签名显示正常，说明修复成功！")
    else:
        print("❌ 部分测试失败，需要进一步调试")

if __name__ == "__main__":
    main()
