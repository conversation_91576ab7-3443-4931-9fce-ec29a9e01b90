#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试签名数据库问题
"""

import os
import sys

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def check_database_signatures():
    """检查数据库中的签名数据"""
    print("=== 检查数据库中的签名数据 ===")
    
    try:
        from app import create_app
        from app.models import db, Signature, File, User
        
        # 创建应用上下文
        app = create_app()
        with app.app_context():
            print("✅ 应用上下文创建成功")
            
            # 检查数据库连接
            try:
                db.engine.execute('SELECT 1')
                print("✅ 数据库连接正常")
            except Exception as e:
                print(f"❌ 数据库连接失败: {e}")
                return False
            
            # 检查signature表结构
            print("\n--- 检查signature表结构 ---")
            try:
                from sqlalchemy import inspect
                inspector = inspect(db.engine)
                columns = inspector.get_columns('signature')
                
                print("signature表字段:")
                for col in columns:
                    print(f"  - {col['name']}: {col['type']}")
                
                # 检查关键字段
                column_names = [col['name'] for col in columns]
                required_fields = ['signature_type', 'signature_position', 'file_id']
                
                for field in required_fields:
                    if field in column_names:
                        print(f"✅ {field} 字段存在")
                    else:
                        print(f"❌ {field} 字段缺失")
                        
            except Exception as e:
                print(f"❌ 检查表结构失败: {e}")
                return False
            
            # 查询所有签名记录
            print("\n--- 查询签名记录 ---")
            try:
                total_signatures = Signature.query.count()
                print(f"总签名数量: {total_signatures}")
                
                if total_signatures > 0:
                    # 查询最近的几个签名
                    recent_signatures = Signature.query.order_by(Signature.signature_date.desc()).limit(5).all()
                    
                    print("\n最近的签名记录:")
                    for i, sig in enumerate(recent_signatures):
                        print(f"  签名 {i+1}:")
                        print(f"    ID: {sig.id}")
                        print(f"    用户ID: {sig.user_id}")
                        print(f"    文件ID: {sig.file_id}")
                        print(f"    签名类型: {getattr(sig, 'signature_type', 'N/A')}")
                        print(f"    签名位置: {getattr(sig, 'signature_position', 'N/A')}")
                        print(f"    签名日期: {sig.signature_date}")
                        print(f"    数据长度: {len(sig.signature_data) if sig.signature_data else 0}")
                        
                        # 检查关联的文件
                        if hasattr(sig, 'file') and sig.file:
                            print(f"    文件名: {sig.file.original_filename}")
                        
                        # 检查关联的用户
                        if hasattr(sig, 'user') and sig.user:
                            print(f"    用户名: {sig.user.username}")
                        
                        print()
                
                # 按签名类型分组统计
                print("--- 按签名类型统计 ---")
                if 'signature_type' in column_names:
                    handwriting_count = Signature.query.filter_by(signature_type='handwriting').count()
                    print(f"手写签名数量: {handwriting_count}")
                    
                    # 查询所有不同的签名类型
                    types = db.session.query(Signature.signature_type).distinct().all()
                    print("所有签名类型:")
                    for type_tuple in types:
                        type_name = type_tuple[0] if type_tuple[0] else 'NULL'
                        count = Signature.query.filter_by(signature_type=type_name if type_name != 'NULL' else None).count()
                        print(f"  - {type_name}: {count}")
                else:
                    print("⚠️ signature_type字段不存在")
                
                # 检查PDF文件的签名
                print("\n--- 检查PDF文件的签名 ---")
                pdf_files = File.query.filter(File.original_filename.like('%.pdf')).all()
                print(f"PDF文件数量: {len(pdf_files)}")
                
                for pdf_file in pdf_files[:3]:  # 只检查前3个PDF文件
                    print(f"\nPDF文件: {pdf_file.original_filename} (ID: {pdf_file.id})")
                    
                    # 查询该文件的所有签名
                    file_signatures = Signature.query.filter_by(file_id=pdf_file.id).all()
                    print(f"  总签名数: {len(file_signatures)}")
                    
                    # 查询手写签名
                    if 'signature_type' in column_names:
                        handwriting_signatures = Signature.query.filter_by(
                            file_id=pdf_file.id,
                            signature_type='handwriting'
                        ).all()
                        print(f"  手写签名数: {len(handwriting_signatures)}")
                    
                    # 显示签名详情
                    for sig in file_signatures:
                        print(f"    签名ID: {sig.id}, 类型: {getattr(sig, 'signature_type', 'N/A')}, 位置: {getattr(sig, 'signature_position', 'N/A')}")
                
            except Exception as e:
                print(f"❌ 查询签名记录失败: {e}")
                import traceback
                traceback.print_exc()
                return False
            
            return True
            
    except Exception as e:
        print(f"❌ 检查数据库失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_signature_query():
    """测试签名查询逻辑"""
    print("\n=== 测试签名查询逻辑 ===")
    
    try:
        from app import create_app
        from app.models import db, Signature, File
        
        app = create_app()
        with app.app_context():
            # 找一个有签名的PDF文件
            pdf_files = File.query.filter(File.original_filename.like('%.pdf')).all()
            
            if not pdf_files:
                print("❌ 没有找到PDF文件")
                return False
            
            for pdf_file in pdf_files:
                print(f"\n测试文件: {pdf_file.original_filename} (ID: {pdf_file.id})")
                
                # 测试不同的查询方式
                queries = [
                    ("所有签名", Signature.query.filter_by(file_id=pdf_file.id)),
                    ("手写签名", Signature.query.filter_by(file_id=pdf_file.id, signature_type='handwriting')),
                ]
                
                for query_name, query in queries:
                    try:
                        results = query.all()
                        print(f"  {query_name}: {len(results)} 条记录")
                        
                        for sig in results:
                            print(f"    - ID: {sig.id}, 类型: {getattr(sig, 'signature_type', 'N/A')}")
                            
                    except Exception as e:
                        print(f"  {query_name}: 查询失败 - {e}")
                
                # 如果找到有签名的文件，就停止
                if Signature.query.filter_by(file_id=pdf_file.id).count() > 0:
                    break
            
            return True
            
    except Exception as e:
        print(f"❌ 测试查询失败: {e}")
        return False

def simulate_export_process():
    """模拟导出过程"""
    print("\n=== 模拟导出过程 ===")
    
    try:
        from app import create_app
        from app.models import db, Signature, File
        
        app = create_app()
        with app.app_context():
            # 找一个有签名的PDF文件
            pdf_files = File.query.filter(File.original_filename.like('%.pdf')).all()
            
            target_file = None
            for pdf_file in pdf_files:
                signature_count = Signature.query.filter_by(file_id=pdf_file.id).count()
                if signature_count > 0:
                    target_file = pdf_file
                    break
            
            if not target_file:
                print("❌ 没有找到有签名的PDF文件")
                return False
            
            print(f"目标文件: {target_file.original_filename} (ID: {target_file.id})")
            
            # 模拟导出函数中的查询
            signatures = Signature.query.filter_by(
                file_id=target_file.id,
                signature_type='handwriting'
            ).all()
            
            print(f"查询到的签名数量: {len(signatures)}")
            
            if not signatures:
                print("❌ 没有查询到手写签名")
                
                # 尝试查询所有签名
                all_signatures = Signature.query.filter_by(file_id=target_file.id).all()
                print(f"该文件的所有签名数量: {len(all_signatures)}")
                
                for sig in all_signatures:
                    print(f"  签名ID: {sig.id}")
                    print(f"  签名类型: {getattr(sig, 'signature_type', 'N/A')}")
                    print(f"  签名位置: {getattr(sig, 'signature_position', 'N/A')}")
                    print()
                
                return False
            else:
                print("✅ 查询到手写签名")
                
                for i, sig in enumerate(signatures):
                    print(f"  签名 {i+1}:")
                    print(f"    ID: {sig.id}")
                    print(f"    数据长度: {len(sig.signature_data) if sig.signature_data else 0}")
                    print(f"    位置: {getattr(sig, 'signature_position', 'N/A')}")
                    
                    # 验证签名数据
                    if sig.signature_data and sig.signature_data.startswith('data:image/'):
                        print(f"    数据格式: 正确的Base64格式")
                    else:
                        print(f"    数据格式: 可能有问题")
                
                return True
            
    except Exception as e:
        print(f"❌ 模拟导出过程失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("=== 签名数据库调试工具 ===")
    
    # 检查数据库
    if not check_database_signatures():
        print("\n❌ 数据库检查失败，请先解决数据库问题")
        return
    
    # 测试查询
    if not test_signature_query():
        print("\n❌ 查询测试失败")
        return
    
    # 模拟导出过程
    if not simulate_export_process():
        print("\n❌ 导出过程模拟失败")
        print("\n可能的问题:")
        print("1. 签名记录的signature_type字段值不是'handwriting'")
        print("2. 签名记录的file_id字段值不正确")
        print("3. 数据库中没有实际的签名数据")
        return
    
    print("\n✅ 所有检查都通过了！")
    print("如果数据库中有正确的签名数据，但PDF导出仍然没有签名，")
    print("问题可能在于PDF处理逻辑。")

if __name__ == "__main__":
    main()
