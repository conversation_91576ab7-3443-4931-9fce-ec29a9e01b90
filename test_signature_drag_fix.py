#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PDF签名拖拽功能修复验证脚本
测试手写签名是否能够正常移动
"""

import os
import re
from pathlib import Path

def test_drag_functionality_fix():
    """测试拖拽功能修复"""
    print("🔧 测试PDF签名拖拽功能修复...")
    
    html_file = Path('app/templates/preview/pdf_sign.html')
    
    if not html_file.exists():
        print("❌ PDF签名页面文件不存在")
        return False
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查修复后的关键特征
        checks = [
            {
                'pattern': r'addEventListener\([\'"]mousedown[\'"]',
                'description': '使用addEventListener绑定鼠标按下事件',
                'required': True
            },
            {
                'pattern': r'addEventListener\([\'"]mousemove[\'"]',
                'description': '使用addEventListener绑定鼠标移动事件',
                'required': True
            },
            {
                'pattern': r'addEventListener\([\'"]mouseup[\'"]',
                'description': '使用addEventListener绑定鼠标释放事件',
                'required': True
            },
            {
                'pattern': r'addEventListener\([\'"]touchstart[\'"]',
                'description': '支持触摸设备的触摸开始事件',
                'required': True
            },
            {
                'pattern': r'addEventListener\([\'"]touchmove[\'"]',
                'description': '支持触摸设备的触摸移动事件',
                'required': True
            },
            {
                'pattern': r'addEventListener\([\'"]touchend[\'"]',
                'description': '支持触摸设备的触摸结束事件',
                'required': True
            },
            {
                'pattern': r'let dragState = \{',
                'description': '独立的拖拽状态管理',
                'required': True
            },
            {
                'pattern': r'function handleMouseDown\(',
                'description': '独立的鼠标按下处理函数',
                'required': True
            },
            {
                'pattern': r'function handleMouseMove\(',
                'description': '独立的鼠标移动处理函数',
                'required': True
            },
            {
                'pattern': r'function handleMouseUp\(',
                'description': '独立的鼠标释放处理函数',
                'required': True
            },
            {
                'pattern': r'element\._dragCleanup',
                'description': '事件清理函数',
                'required': True
            },
            {
                'pattern': r'removeEventListener',
                'description': '事件监听器清理',
                'required': True
            }
        ]
        
        all_passed = True
        
        for check in checks:
            if re.search(check['pattern'], content):
                print(f"  ✅ {check['description']}")
            else:
                print(f"  ❌ {check['description']} - 未找到")
                if check['required']:
                    all_passed = False
        
        # 检查是否移除了有问题的旧代码
        old_patterns = [
            {
                'pattern': r'document\.onmousemove\s*=',
                'description': '旧的document.onmousemove赋值方式'
            },
            {
                'pattern': r'document\.onmouseup\s*=',
                'description': '旧的document.onmouseup赋值方式'
            }
        ]
        
        print("\n🔍 检查是否移除了有问题的旧代码:")
        for check in old_patterns:
            if re.search(check['pattern'], content):
                print(f"  ⚠️  仍存在: {check['description']}")
                all_passed = False
            else:
                print(f"  ✅ 已移除: {check['description']}")
        
        return all_passed
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")
        return False

def test_signature_initialization():
    """测试签名初始化功能"""
    print("\n🚀 测试签名初始化功能...")
    
    html_file = Path('app/templates/preview/pdf_sign.html')
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        checks = [
            {
                'pattern': r'initializeExistingSignatures',
                'description': '现有签名初始化函数'
            },
            {
                'pattern': r'addDragFunctionality\(signature\)',
                'description': '为现有签名添加拖拽功能'
            },
            {
                'pattern': r'setTimeout\(initializeExistingSignatures',
                'description': '延迟初始化现有签名'
            },
            {
                'pattern': r'querySelectorAll\([\'"]\.signature-item[\'"]',
                'description': '查找所有签名元素'
            }
        ]
        
        for check in checks:
            if re.search(check['pattern'], content):
                print(f"  ✅ {check['description']}")
            else:
                print(f"  ❌ {check['description']} - 未找到")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def test_css_styles():
    """测试CSS样式"""
    print("\n🎨 测试拖拽相关CSS样式...")
    
    html_file = Path('app/templates/preview/pdf_sign.html')
    
    try:
        with open(html_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        css_checks = [
            {
                'pattern': r'\.signature-item\s*\{',
                'description': '签名元素基础样式'
            },
            {
                'pattern': r'cursor:\s*move',
                'description': '移动光标样式'
            },
            {
                'pattern': r'\.signature-item:hover',
                'description': '签名悬停样式'
            },
            {
                'pattern': r'\.signature-item\.dragging',
                'description': '拖拽状态样式'
            },
            {
                'pattern': r'pointer-events:\s*auto',
                'description': '指针事件启用'
            }
        ]
        
        for check in css_checks:
            if re.search(check['pattern'], content):
                print(f"  ✅ {check['description']}")
            else:
                print(f"  ❌ {check['description']} - 未找到")
        
    except Exception as e:
        print(f"❌ 检查失败: {e}")

def main():
    """主函数"""
    print("=" * 60)
    print("🔧 PDF签名拖拽功能修复验证")
    print("=" * 60)
    
    # 测试拖拽功能修复
    drag_fix_success = test_drag_functionality_fix()
    
    # 测试签名初始化
    test_signature_initialization()
    
    # 测试CSS样式
    test_css_styles()
    
    print("\n" + "=" * 60)
    if drag_fix_success:
        print("✅ 拖拽功能修复验证通过！")
        print("\n📋 修复内容总结:")
        print("  • 使用addEventListener替代直接事件赋值")
        print("  • 为每个签名创建独立的拖拽状态")
        print("  • 添加触摸设备支持")
        print("  • 提供事件清理机制")
        print("  • 修复多签名拖拽冲突问题")
    else:
        print("❌ 拖拽功能修复验证失败！")
    print("=" * 60)

if __name__ == "__main__":
    main()
