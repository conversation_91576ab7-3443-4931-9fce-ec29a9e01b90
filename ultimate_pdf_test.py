#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
终极PDF签名测试 - 验证修复是否有效
"""

import base64
import io
import os
import tempfile
from datetime import datetime

def test_signature_processing():
    """测试签名处理"""
    print("=== 终极PDF签名测试 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PIL import Image, ImageDraw
        
        print("1. 创建测试签名...")
        
        # 创建签名图片
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Test Signature", fill='black')
        draw.line([(20, 60), (180, 60)], fill='black', width=2)
        
        # 转换为Base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        signature_data = f"data:image/png;base64,{base64.b64encode(buffer.getvalue()).decode()}"
        
        print(f"   签名数据长度: {len(signature_data)}")
        
        print("2. 创建PDF...")
        
        # 创建PDF
        page_width, page_height = A4
        output_buffer = io.BytesIO()
        c = canvas.Canvas(output_buffer, pagesize=A4)
        
        # 添加内容
        c.drawString(50, page_height - 50, "已签名文档测试")
        c.drawString(50, page_height - 80, f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        # 处理签名
        if signature_data.startswith('data:image/'):
            clean_data = signature_data.split(',')[1]
        else:
            clean_data = signature_data
        
        # 解码图片
        image_data = base64.b64decode(clean_data)
        print(f"   解码后图片大小: {len(image_data)} bytes")
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        temp_file.write(image_data)
        temp_file.close()
        
        print(f"   临时文件: {temp_file.name}")
        
        # 在PDF中绘制签名
        pdf_x = 100
        pdf_y = page_height - 300
        width = 150
        height = 75
        
        c.drawImage(temp_file.name, pdf_x, pdf_y, width=width, height=height)
        c.drawString(pdf_x, pdf_y - 20, "签名者: Test User")
        c.drawString(pdf_x, pdf_y - 35, f"时间: {datetime.now().strftime('%Y-%m-%d %H:%M')}")
        
        # 完成PDF
        c.save()
        
        # 清理临时文件
        os.remove(temp_file.name)
        
        # 保存结果
        output_buffer.seek(0)
        with open('ultimate_test_signed.pdf', 'wb') as f:
            f.write(output_buffer.getvalue())
        
        print(f"3. 处理完成:")
        print(f"   输出文件: ultimate_test_signed.pdf")
        print(f"   文件大小: {len(output_buffer.getvalue())} bytes")
        
        print("✅ 签名处理成功！")
        print("\n请打开 ultimate_test_signed.pdf 检查签名是否显示")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("这个测试将验证PDF签名处理的核心逻辑")
    print()
    
    # 检查依赖
    try:
        import reportlab
        from PIL import Image
        print("✅ 依赖库检查通过")
    except ImportError as e:
        print(f"❌ 缺少依赖库: {e}")
        return
    
    # 运行测试
    success = test_signature_processing()
    
    if success:
        print("\n✅ 测试成功！如果生成的PDF包含签名，说明核心逻辑正常。")
        print("如果应用程序中仍然没有签名，问题可能在于:")
        print("1. 数据库查询条件")
        print("2. 签名数据格式")
        print("3. 文件路径问题")
    else:
        print("\n❌ 测试失败，需要检查基础环境")

if __name__ == "__main__":
    main()
