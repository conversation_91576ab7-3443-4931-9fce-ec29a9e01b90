{% extends "base.html" %}
{% block content %}
<div class="container-fluid p-0">
    <div class="d-flex justify-content-between align-items-center mb-4">
        <nav aria-label="breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ url_for('main.index') }}">
                        <i class="fas fa-home home-icon"></i>
                        {{ t('Home') }}
                    </a>
                </li>
                <li class="breadcrumb-item active">{{ t('My Signatures') }}</li>
            </ol>
        </nav>
    </div>

    <div class="row">
        <div class="col-md-6">
            <div class="card mb-4">
                <div class="card-header d-flex justify-content-between align-items-center">
                    <h5 class="card-title mb-0">{{ t('Add New Signature') }}</h5>
                </div>
                <div class="card-body">
                    <div class="alert alert-info">
                        {{ t('Add a signature that you can quickly use when signing documents.') }}
                    </div>

                    <div class="mb-3">
                        <label for="signatureDescription" class="form-label">{{ t('Signature Description') }}</label>
                        <input type="text" class="form-control" id="signatureDescription" placeholder="{{ t('e.g. My official signature') }}">
                    </div>

                    <div class="signature-pad-container mb-4">
                        <h6>{{ t('Draw Your Signature Below') }}:</h6>
                        <div class="signature-pad-wrapper">
                            <canvas id="signaturePad" class="signature-pad"></canvas>
                        </div>
                        <div class="signature-pad-controls mt-2 d-flex gap-2">
                            <button id="clearSignature" class="btn btn-outline-secondary btn-sm">
                                <i class="fas fa-eraser"></i> {{ t('Clear') }}
                            </button>
                            <div class="form-check ms-3 d-flex align-items-center">
                                <input class="form-check-input" type="checkbox" id="setAsDefault" checked>
                                <label class="form-check-label ms-2" for="setAsDefault">
                                    {{ t('Set as default signature') }}
                                </label>
                            </div>
                        </div>
                    </div>

                    <button id="saveSignature" class="btn btn-primary">
                        <i class="fas fa-save"></i> {{ t('Save Signature') }}
                    </button>
                </div>
            </div>
        </div>

        <div class="col-md-6">
            <div class="card">
                <div class="card-header">
                    <h5 class="card-title mb-0">{{ t('My Saved Signatures') }}</h5>
                </div>
                <div class="card-body">
                    <div id="signaturesList">
                        {% if signatures %}
                            {% for signature in signatures %}
                            <div class="signature-item mb-3 p-3 border rounded">
                                <div class="d-flex justify-content-between align-items-start mb-2">
                                    <div>
                                        <strong>{{ signature.description or t('Signature') + ' #' + signature.id|string }}</strong>
                                        {% if signature.is_default %}
                                        <span class="badge bg-success ms-2">{{ t('Default') }}</span>
                                        {% endif %}
                                    </div>
                                    <div class="signature-date">
                                        {{ signature.created_at|format_datetime }}
                                    </div>
                                </div>
                                <div class="signature-image mb-2">
                                    <img src="{{ signature.signature_data }}" alt="{{ t('Signature') }}" class="img-fluid">
                                </div>
                                <div class="signature-actions d-flex gap-2">
                                    {% if not signature.is_default %}
                                    <button class="btn btn-sm btn-outline-primary set-default-signature" data-signature-id="{{ signature.id }}">
                                        <i class="fas fa-check"></i> {{ t('Set as Default') }}
                                    </button>
                                    {% endif %}
                                    <button class="btn btn-sm btn-outline-success export-signature" data-signature-id="{{ signature.id }}">
                                        <i class="fas fa-download"></i> {{ t('Export Template') }}
                                    </button>
                                    <button class="btn btn-sm btn-outline-danger delete-signature" data-signature-id="{{ signature.id }}">
                                        <i class="fas fa-trash"></i> {{ t('Delete') }}
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        {% else %}
                            <div class="alert alert-info">
                                {{ t('You have not saved any signatures yet. Create your first signature using the form on the left.') }}
                            </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- 删除确认模态框 -->
<div class="modal fade" id="deleteSignatureModal" tabindex="-1" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">{{ t('Confirm Deletion') }}</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <div class="modal-body">
                {{ t('Are you sure you want to delete this signature?') }}
                <input type="hidden" id="signatureIdToDelete">
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">{{ t('Cancel') }}</button>
                <button type="button" class="btn btn-danger" id="confirmDeleteSignature">{{ t('Delete') }}</button>
            </div>
        </div>
    </div>
</div>

<style>
.signature-pad-wrapper {
    border: 1px solid #ddd;
    border-radius: 4px;
    position: relative;
}

.signature-pad {
    width: 100%;
    height: 200px;
    background-color: #fff;
    border-radius: 4px;
}

.signature-item {
    background-color: #f9f9f9;
}

.signature-image img {
    max-height: 100px;
    border: 1px solid #eee;
    border-radius: 4px;
    background-color: #fff;
}
</style>

<script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
<script>
document.addEventListener('DOMContentLoaded', function() {
    // 初始化签名板
    const canvas = document.getElementById('signaturePad');
    const signaturePad = new SignaturePad(canvas, {
        backgroundColor: 'rgb(255, 255, 255)',
        penColor: 'rgb(0, 0, 0)'
    });
    
    // 自适应调整画布大小
    function resizeCanvas() {
        const ratio = Math.max(window.devicePixelRatio || 1, 1);
        canvas.width = canvas.offsetWidth * ratio;
        canvas.height = canvas.offsetHeight * ratio;
        canvas.getContext("2d").scale(ratio, ratio);
        signaturePad.clear(); // 调整大小后清除
    }
    
    window.addEventListener("resize", resizeCanvas);
    resizeCanvas();
    
    // 清除签名
    document.getElementById('clearSignature').addEventListener('click', function() {
        signaturePad.clear();
    });
    
    // 保存签名
    document.getElementById('saveSignature').addEventListener('click', function() {
        if (signaturePad.isEmpty()) {
            alert('{{ t("Please provide a signature first") }}');
            return;
        }
        
        const signatureData = signaturePad.toDataURL();
        const description = document.getElementById('signatureDescription').value;
        const setAsDefault = document.getElementById('setAsDefault').checked;
        
        // 创建表单数据
        const formData = new FormData();
        formData.append('signature_data', signatureData);
        formData.append('description', description);
        formData.append('set_as_default', setAsDefault);
        
        // 发送签名数据到服务器
        fetch('{{ url_for("main.add_user_signature") }}', {
            method: 'POST',
            body: formData
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{{ t("Signature saved successfully") }}');
                location.reload(); // 重新加载页面显示新签名
            } else {
                alert(data.message || '{{ t("Failed to save signature") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ t("An error occurred while saving the signature") }}');
        });
    });
    
    // 设置默认签名
    document.querySelectorAll('.set-default-signature').forEach(button => {
        button.addEventListener('click', function() {
            const signatureId = this.getAttribute('data-signature-id');
            
            fetch(`{{ url_for('main.set_default_signature', signature_id=0) }}`.replace('0', signatureId), {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    alert('{{ t("Default signature updated successfully") }}');
                    location.reload(); // 重新加载页面更新显示
                } else {
                    alert(data.message || '{{ t("Failed to update default signature") }}');
                }
            })
            .catch(error => {
                console.error('Error:', error);
                alert('{{ t("An error occurred while updating default signature") }}');
            });
        });
    });
    
    // 删除签名 - 打开确认窗口
    document.querySelectorAll('.delete-signature').forEach(button => {
        button.addEventListener('click', function() {
            const signatureId = this.getAttribute('data-signature-id');
            document.getElementById('signatureIdToDelete').value = signatureId;
            
            const deleteModal = new bootstrap.Modal(document.getElementById('deleteSignatureModal'));
            deleteModal.show();
        });
    });
    
    // 确认删除签名
    document.getElementById('confirmDeleteSignature').addEventListener('click', function() {
        const signatureId = document.getElementById('signatureIdToDelete').value;
        
        fetch(`{{ url_for('main.delete_user_signature', signature_id=0) }}`.replace('0', signatureId), {
            method: 'POST'
        })
        .then(response => response.json())
        .then(data => {
            if (data.success) {
                alert('{{ t("Signature deleted successfully") }}');
                location.reload(); // 重新加载页面更新显示
            } else {
                alert(data.message || '{{ t("Failed to delete signature") }}');
            }
        })
        .catch(error => {
            console.error('Error:', error);
            alert('{{ t("An error occurred while deleting the signature") }}');
        });
        
        // 关闭模态框
        const deleteModal = bootstrap.Modal.getInstance(document.getElementById('deleteSignatureModal'));
        deleteModal.hide();
    });

    // 导出签名
    document.querySelectorAll('.export-signature').forEach(button => {
        button.addEventListener('click', function() {
            const signatureId = this.getAttribute('data-signature-id');

            // 直接跳转到导出URL
            window.location.href = `/user_signature/export/${signatureId}`;
        });
    });
});
</script>
{% endblock %} 