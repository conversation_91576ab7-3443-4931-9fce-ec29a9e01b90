#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PDF签名合并功能
"""

import base64
import io
import os
from PIL import Image, ImageDraw

def test_pdf_merge():
    """测试PDF合并功能"""
    print("=== 测试PDF合并功能 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.utils import ImageReader
        from PyPDF2 import PdfReader, PdfWriter
        
        # 1. 创建原始PDF
        print("1. 创建原始PDF...")
        original_pdf = io.BytesIO()
        c = canvas.Canvas(original_pdf, pagesize=A4)
        c.drawString(100, 750, "这是原始PDF文档")
        c.drawString(100, 700, "签名将被添加到此文档")
        c.rect(100, 400, 200, 100, stroke=1, fill=0)
        c.drawString(110, 440, "签名区域")
        c.save()
        
        # 2. 创建签名图片
        print("2. 创建签名图片...")
        img = Image.new('RGBA', (200, 100), (255, 255, 255, 0))  # 透明背景
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Test Signature", fill=(0, 0, 0, 255))
        draw.line([(20, 60), (180, 60)], fill=(0, 0, 0, 255), width=2)
        
        # 转换为Base64
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        signature_data = base64.b64encode(img_buffer.getvalue()).decode()
        
        # 3. 创建签名覆盖层
        print("3. 创建签名覆盖层...")
        page_width, page_height = A4
        overlay_pdf = io.BytesIO()
        
        c2 = canvas.Canvas(overlay_pdf, pagesize=A4)
        
        # 解码签名图片
        image_data = base64.b64decode(signature_data)
        image_stream = io.BytesIO(image_data)
        
        # 在指定位置绘制签名
        signature_x = 120
        signature_y = page_height - 500  # PDF坐标系转换
        signature_width = 150
        signature_height = 75
        
        c2.drawImage(
            ImageReader(image_stream),
            signature_x, signature_y,
            width=signature_width,
            height=signature_height,
            mask='auto'
        )
        
        c2.save()
        
        # 4. 合并PDF
        print("4. 合并PDF...")
        original_pdf.seek(0)
        overlay_pdf.seek(0)
        
        reader = PdfReader(original_pdf)
        overlay_reader = PdfReader(overlay_pdf)
        writer = PdfWriter()
        
        # 合并第一页
        page = reader.pages[0]
        overlay_page = overlay_reader.pages[0]
        page.merge_page(overlay_page)
        writer.add_page(page)
        
        # 5. 保存结果
        print("5. 保存结果...")
        result_pdf = io.BytesIO()
        writer.write(result_pdf)
        result_pdf.seek(0)
        
        # 保存到文件以便检查
        with open('test_signed.pdf', 'wb') as f:
            f.write(result_pdf.getvalue())
        
        print("✅ PDF合并测试成功!")
        print(f"   原始PDF大小: {len(original_pdf.getvalue())} bytes")
        print(f"   覆盖层PDF大小: {len(overlay_pdf.getvalue())} bytes")
        print(f"   合并后PDF大小: {len(result_pdf.getvalue())} bytes")
        print(f"   输出文件: test_signed.pdf")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF合并测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n=== 测试坐标转换 ===")
    
    from reportlab.lib.pagesizes import A4
    
    page_width, page_height = A4
    print(f"PDF页面尺寸: {page_width} x {page_height}")
    
    # 模拟Web坐标
    web_positions = [
        {'x': 100, 'y': 100, 'width': 150, 'height': 75},
        {'x': 200, 'y': 200, 'width': 150, 'height': 75},
        {'x': 300, 'y': 400, 'width': 150, 'height': 75},
    ]
    
    for i, pos in enumerate(web_positions):
        # 转换为PDF坐标
        pdf_x = pos['x']
        pdf_y = page_height - pos['y'] - pos['height']
        
        print(f"签名 {i+1}:")
        print(f"  Web坐标: ({pos['x']}, {pos['y']})")
        print(f"  PDF坐标: ({pdf_x}, {pdf_y})")
        print(f"  尺寸: {pos['width']} x {pos['height']}")
        
        # 检查是否在页面范围内
        if 0 <= pdf_x <= page_width and 0 <= pdf_y <= page_height:
            print(f"  ✅ 位置有效")
        else:
            print(f"  ❌ 位置超出页面范围")
        print()

def test_image_processing():
    """测试图片处理"""
    print("=== 测试图片处理 ===")
    
    try:
        # 创建测试签名
        img = Image.new('RGBA', (200, 100), (255, 255, 255, 0))
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Test Signature", fill=(0, 0, 0, 255))
        draw.line([(20, 60), (180, 60)], fill=(0, 0, 0, 255), width=2)
        
        # 保存为PNG
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        
        # 转换为Base64
        signature_data = f"data:image/png;base64,{base64.b64encode(img_buffer.getvalue()).decode()}"
        
        print(f"✅ 签名图片创建成功")
        print(f"   图片尺寸: {img.size}")
        print(f"   图片模式: {img.mode}")
        print(f"   Base64长度: {len(signature_data)}")
        
        # 测试解码
        if signature_data.startswith('data:image/'):
            clean_data = signature_data.split(',')[1]
        else:
            clean_data = signature_data
            
        decoded_data = base64.b64decode(clean_data)
        
        # 验证解码后的图片
        decoded_img = Image.open(io.BytesIO(decoded_data))
        print(f"✅ Base64解码成功")
        print(f"   解码后尺寸: {decoded_img.size}")
        print(f"   解码后模式: {decoded_img.mode}")
        
        return True
        
    except Exception as e:
        print(f"❌ 图片处理测试失败: {e}")
        return False

if __name__ == "__main__":
    print("=== PDF签名合并测试 ===")
    
    # 测试图片处理
    test_image_processing()
    
    # 测试坐标转换
    test_coordinate_conversion()
    
    # 测试PDF合并
    test_pdf_merge()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都通过，请检查生成的 test_signed.pdf 文件是否包含签名。")
