# PDF签名导出功能说明

## 功能概述

在原有的手写签名功能基础上，新增了将签名嵌入到PDF文件的功能。用户现在可以将包含所有手写签名的PDF文件导出，生成一个完整的已签名PDF文档。

## 新增功能

### 1. 带签名PDF导出
- **位置**: PDF签名页面工具栏
- **按钮**: "导出带签名PDF" (蓝色PDF图标)
- **功能**: 将所有签名嵌入到原始PDF文件中，生成新的已签名PDF

### 2. 用户签名模板导出
- **位置**: 用户签名管理页面
- **功能**: 导出保存的签名模板为PNG图片文件（独立功能）

## 技术实现

### 后端路由

#### 1. 带签名PDF导出
```python
@main.route('/pdf/export_with_signature/<int:file_id>')
@login_required
def export_pdf_with_signature(file_id):
```
- 权限检查：只有有权限访问文件的用户可以导出
- PDF处理：使用PyPDF2和ReportLab处理PDF文件
- 签名嵌入：将所有签名按位置嵌入到PDF页面中
- 文件命名：`{原文件名}_signed_{时间戳}.pdf`

#### 2. 用户签名模板导出
```python
@main.route('/user_signature/export/<int:user_signature_id>')
@login_required
def export_user_signature(user_signature_id):
```
- 文件命名：`signature_template_{用户名}_{描述}_{时间戳}.png`
- 特殊字符处理：清理文件名中的非法字符

#### 3. PDF处理核心函数
```python
def create_signed_pdf(original_pdf_path, signatures):
def create_signature_overlay(signatures, page_width, page_height):
```
- PDF读取和写入
- 签名位置坐标转换
- 图片透明度处理

### 前端界面

#### 1. PDF签名页面 (pdf_sign.html)
- 工具栏添加"导出带签名PDF"按钮
- 移除了单个签名的导出按钮（现在统一导出整个PDF）
- JavaScript函数：`exportSignedPDF()`

#### 2. 用户签名管理页面 (user_signatures.html)
- 保留签名模板的导出功能
- 按钮文本改为"导出模板"以区分功能
- JavaScript函数：处理模板导出点击事件

#### 3. 普通签名页面 (signature.html)
- 移除了签名导出按钮
- 用户需要通过PDF页面来导出带签名的文档

## 文件格式和命名

### 文件格式
- **PDF文件**: application/pdf
- **签名模板**: image/png
- **编码**: 从Base64解码后的原始图片数据

### 文件命名规则

#### 带签名PDF
```
{原文件名}_signed_{YYYYMMDD_HHMMSS}.pdf
```
例如：`contract_signed_20240115_143022.pdf`

#### 签名模板
```
signature_template_{用户名}_{描述}_{YYYYMMDD_HHMMSS}.png
```
例如：`signature_template_admin_PDF签名_20240115_143022.png`

## 权限控制

### 导出权限
- **签名所有者**: 可以导出自己的签名
- **超级管理员**: 可以导出任何用户的签名
- **普通用户**: 只能导出自己的签名

### 文件访问权限
- 导出的文件通过Flask的`send_file`函数直接返回
- 不在服务器上永久存储
- 临时文件会自动清理

## 使用方法

### 1. 导出带签名的PDF
1. 在PDF签名页面完成所有签名
2. 点击工具栏中的"导出带签名PDF"按钮
3. 系统会将所有签名嵌入到原始PDF中
4. 浏览器会自动下载包含签名的新PDF文件

### 2. 导出签名模板
1. 在用户签名管理页面找到要导出的签名模板
2. 点击"导出模板"按钮
3. 浏览器会自动下载PNG文件

## 错误处理

### 常见错误情况
1. **权限不足**: 显示"您没有权限导出此签名"
2. **签名不存在**: 返回404错误
3. **Base64解码失败**: 显示"导出签名失败"
4. **文件创建失败**: 显示相应错误信息

### 日志记录
- 所有导出操作都会记录到用户操作日志中
- 包含操作类型、文件名、用户信息等

## 依赖库

### Python库
- **PyPDF2**: PDF文件读取和写入
- **ReportLab**: PDF生成和图片嵌入
- **Pillow**: 图片处理库，用于Base64转图片
- **base64**: Python内置库，用于Base64编解码
- **io**: Python内置库，用于内存文件操作

### 前端库
- **Bootstrap 5**: UI框架
- **Font Awesome**: 图标库

## 安全考虑

### 数据安全
- 导出的文件不包含敏感的元数据
- 只导出签名图片本身
- 临时文件会及时清理

### 访问控制
- 严格的权限检查
- 防止未授权访问他人签名
- 操作日志记录

## 兼容性

### 浏览器兼容性
- 支持所有现代浏览器
- 使用标准的HTML5下载功能

### 文件兼容性
- PNG格式广泛支持
- 可在各种图片查看器和编辑器中打开

## 未来扩展

### 可能的改进
1. 支持多页PDF的签名分页处理
2. 添加签名位置的精确控制
3. 支持签名的数字证书验证
4. 添加PDF密码保护功能
5. 支持签名的时间戳和元数据嵌入
6. 优化大文件PDF的处理性能

## 技术细节

### 坐标系转换
- **Web坐标系**: 原点在左上角，Y轴向下
- **PDF坐标系**: 原点在左下角，Y轴向上
- **转换公式**: `pdf_y = page_height - web_y - signature_height`

### PDF处理流程
1. 读取原始PDF文件
2. 获取页面尺寸信息
3. 为每个签名创建覆盖层
4. 将签名图片按位置绘制到覆盖层
5. 将覆盖层合并到原始页面
6. 生成新的PDF文件

### 图片处理
- 支持透明背景的PNG图片
- 自动处理Base64编码的签名数据
- 保持签名的原始比例和质量
