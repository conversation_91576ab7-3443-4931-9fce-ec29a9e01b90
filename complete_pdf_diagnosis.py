#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整的PDF签名问题诊断工具
"""

import base64
import io
import os
import sys
import tempfile

def step1_check_dependencies():
    """步骤1: 检查所有依赖库"""
    print("=== 步骤1: 检查依赖库 ===")
    
    deps = {
        'PyPDF2': None,
        'reportlab': None,
        'PIL': None
    }
    
    try:
        import PyPDF2
        deps['PyPDF2'] = PyPDF2.__version__
        print(f"✅ PyPDF2: {PyPDF2.__version__}")
    except ImportError:
        print("❌ PyPDF2 未安装")
        return False
    
    try:
        import reportlab
        deps['reportlab'] = reportlab.Version
        print(f"✅ ReportLab: {reportlab.Version}")
    except ImportError:
        print("❌ ReportLab 未安装")
        return False
    
    try:
        from PIL import Image
        deps['PIL'] = Image.__version__
        print(f"✅ Pillow: {Image.__version__}")
    except ImportError:
        print("❌ Pillow 未安装")
        return False
    
    return True

def step2_test_basic_pdf():
    """步骤2: 测试基础PDF创建"""
    print("\n=== 步骤2: 测试基础PDF创建 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)
        c.drawString(100, 750, "测试PDF")
        c.save()
        
        with open('step2_basic.pdf', 'wb') as f:
            f.write(buffer.getvalue())
        
        print(f"✅ 基础PDF创建成功: {len(buffer.getvalue())} bytes")
        return True
    except Exception as e:
        print(f"❌ 基础PDF创建失败: {e}")
        return False

def step3_test_image_creation():
    """步骤3: 测试签名图片创建"""
    print("\n=== 步骤3: 测试签名图片创建 ===")
    
    try:
        from PIL import Image, ImageDraw
        
        # 创建签名图片
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Test Signature", fill='black')
        draw.line([(20, 60), (180, 60)], fill='black', width=2)
        
        # 保存图片
        img.save('step3_signature.png')
        
        # 转换为Base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        base64_data = base64.b64encode(buffer.getvalue()).decode()
        data_url = f"data:image/png;base64,{base64_data}"
        
        print(f"✅ 签名图片创建成功")
        print(f"   图片尺寸: {img.size}")
        print(f"   Base64长度: {len(data_url)}")
        
        # 测试解码
        clean_data = data_url.split(',')[1]
        decoded_data = base64.b64decode(clean_data)
        
        # 验证解码
        decoded_img = Image.open(io.BytesIO(decoded_data))
        print(f"   解码验证: {decoded_img.size}")
        
        return data_url
    except Exception as e:
        print(f"❌ 签名图片创建失败: {e}")
        return None

def step4_test_image_in_pdf():
    """步骤4: 测试在PDF中添加图片"""
    print("\n=== 步骤4: 测试PDF中添加图片 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # 使用步骤3创建的图片
        if not os.path.exists('step3_signature.png'):
            print("❌ 找不到签名图片文件")
            return False
        
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)
        c.drawString(100, 750, "PDF with Image")
        c.drawImage('step3_signature.png', 100, 600, width=150, height=75)
        c.save()
        
        with open('step4_pdf_with_image.pdf', 'wb') as f:
            f.write(buffer.getvalue())
        
        print(f"✅ PDF图片添加成功: {len(buffer.getvalue())} bytes")
        return True
    except Exception as e:
        print(f"❌ PDF图片添加失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step5_test_base64_to_pdf():
    """步骤5: 测试Base64到PDF的完整流程"""
    print("\n=== 步骤5: 测试Base64到PDF流程 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # 使用步骤3的Base64数据
        signature_data = step3_test_image_creation()
        if not signature_data:
            return False
        
        # 解码Base64
        clean_data = signature_data.split(',')[1]
        image_data = base64.b64decode(clean_data)
        
        # 创建临时文件
        temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
        temp_file.write(image_data)
        temp_file.close()
        
        print(f"   临时文件: {temp_file.name}")
        
        # 创建PDF
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)
        c.drawString(100, 750, "Base64 to PDF Test")
        c.drawImage(temp_file.name, 100, 600, width=150, height=75)
        c.save()
        
        # 清理临时文件
        os.remove(temp_file.name)
        
        with open('step5_base64_to_pdf.pdf', 'wb') as f:
            f.write(buffer.getvalue())
        
        print(f"✅ Base64到PDF成功: {len(buffer.getvalue())} bytes")
        return True
    except Exception as e:
        print(f"❌ Base64到PDF失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step6_test_pdf_merge():
    """步骤6: 测试PDF合并"""
    print("\n=== 步骤6: 测试PDF合并 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PyPDF2 import PdfReader, PdfWriter
        
        # 创建原始PDF
        original = io.BytesIO()
        c1 = canvas.Canvas(original, pagesize=A4)
        c1.drawString(100, 750, "原始文档")
        c1.drawString(100, 700, "这里将添加签名")
        c1.save()
        
        # 创建覆盖层
        overlay = io.BytesIO()
        c2 = canvas.Canvas(overlay, pagesize=A4)
        
        # 使用步骤3的图片
        if os.path.exists('step3_signature.png'):
            page_width, page_height = A4
            c2.drawImage('step3_signature.png', 100, page_height - 300, width=150, height=75)
        
        c2.save()
        
        # 合并PDF
        original.seek(0)
        overlay.seek(0)
        
        reader = PdfReader(original)
        overlay_reader = PdfReader(overlay)
        writer = PdfWriter()
        
        page = reader.pages[0]
        if len(overlay_reader.pages) > 0:
            overlay_page = overlay_reader.pages[0]
            page.merge_page(overlay_page)
        
        writer.add_page(page)
        
        result = io.BytesIO()
        writer.write(result)
        
        with open('step6_merged.pdf', 'wb') as f:
            f.write(result.getvalue())
        
        print(f"✅ PDF合并成功: {len(result.getvalue())} bytes")
        return True
    except Exception as e:
        print(f"❌ PDF合并失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def step7_simulate_app_process():
    """步骤7: 模拟应用程序完整流程"""
    print("\n=== 步骤7: 模拟应用程序流程 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PyPDF2 import PdfReader, PdfWriter
        from PIL import Image, ImageDraw
        
        # 1. 创建原始PDF文件（模拟用户上传）
        original_path = 'app_original.pdf'
        c = canvas.Canvas(original_path, pagesize=A4)
        c.drawString(100, 750, "用户上传的PDF文档")
        c.drawString(100, 700, "合同内容...")
        c.drawString(100, 400, "签名区域:")
        c.rect(100, 300, 200, 80, stroke=1, fill=0)
        c.save()
        
        print("   1. 原始PDF创建完成")
        
        # 2. 创建签名数据（模拟前端发送）
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "用户签名", fill='black')
        draw.line([(20, 60), (180, 60)], fill='black', width=2)
        
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        signature_data = f"data:image/png;base64,{base64.b64encode(buffer.getvalue()).decode()}"
        signature_position = "120,320,160,60"  # x,y,width,height
        
        print("   2. 签名数据创建完成")
        
        # 3. 模拟应用程序处理逻辑
        with open(original_path, 'rb') as f:
            reader = PdfReader(f)
            writer = PdfWriter()
            
            # 获取页面尺寸
            first_page = reader.pages[0]
            page_width = float(first_page.mediabox.width)
            page_height = float(first_page.mediabox.height)
            
            print(f"   3. PDF页面尺寸: {page_width} x {page_height}")
            
            # 解析签名数据
            clean_data = signature_data.split(',')[1]
            image_data = base64.b64decode(clean_data)
            
            # 解析位置
            pos_parts = signature_position.split(',')
            x, y, w, h = map(float, pos_parts)
            
            # 坐标转换
            pdf_x = x
            pdf_y = page_height - y - h
            
            print(f"   4. 坐标转换: Web({x}, {y}) -> PDF({pdf_x}, {pdf_y})")
            
            # 创建覆盖层
            overlay_buffer = io.BytesIO()
            c2 = canvas.Canvas(overlay_buffer, pagesize=(page_width, page_height))
            
            # 使用临时文件
            temp_file = tempfile.NamedTemporaryFile(suffix='.png', delete=False)
            temp_file.write(image_data)
            temp_file.close()
            
            # 绘制签名
            c2.drawImage(temp_file.name, pdf_x, pdf_y, width=w, height=h)
            c2.save()
            
            # 清理临时文件
            os.remove(temp_file.name)
            
            print("   5. 签名覆盖层创建完成")
            
            # 合并PDF
            overlay_buffer.seek(0)
            overlay_reader = PdfReader(overlay_buffer)
            
            for page_num, page in enumerate(reader.pages):
                if page_num == 0 and len(overlay_reader.pages) > 0:
                    page.merge_page(overlay_reader.pages[0])
                writer.add_page(page)
            
            # 保存结果
            with open('app_signed.pdf', 'wb') as output_file:
                writer.write(output_file)
        
        # 清理原始文件
        os.remove(original_path)
        
        print("✅ 应用程序流程模拟成功: app_signed.pdf")
        
        # 验证文件大小
        size = os.path.getsize('app_signed.pdf')
        print(f"   最终文件大小: {size} bytes")
        
        return True
    except Exception as e:
        print(f"❌ 应用程序流程模拟失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_test_files():
    """清理测试文件"""
    test_files = [
        'step2_basic.pdf',
        'step3_signature.png', 
        'step4_pdf_with_image.pdf',
        'step5_base64_to_pdf.pdf',
        'step6_merged.pdf',
        'app_signed.pdf'
    ]
    
    for file in test_files:
        if os.path.exists(file):
            try:
                os.remove(file)
            except:
                pass

def main():
    """主函数"""
    print("=== PDF签名问题完整诊断 ===")
    print("这个工具将逐步测试PDF签名的每个环节")
    print()
    
    # 运行所有测试步骤
    steps = [
        ("检查依赖库", step1_check_dependencies),
        ("基础PDF创建", step2_test_basic_pdf),
        ("签名图片创建", step3_test_image_creation),
        ("PDF中添加图片", step4_test_image_in_pdf),
        ("Base64到PDF", step5_test_base64_to_pdf),
        ("PDF合并", step6_test_pdf_merge),
        ("完整应用流程", step7_simulate_app_process),
    ]
    
    results = []
    
    for name, func in steps:
        if name == "签名图片创建":
            # 这个步骤返回数据而不是布尔值
            result = func()
            results.append(result is not None)
        else:
            result = func()
            results.append(result)
        
        if not results[-1]:
            print(f"\n❌ 在步骤 '{name}' 失败，停止后续测试")
            break
    
    # 总结结果
    print(f"\n=== 诊断结果 ===")
    passed = sum(results)
    total = len(results)
    
    print(f"通过步骤: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有基础功能正常！")
        print("\n生成的测试文件:")
        test_files = ['step2_basic.pdf', 'step4_pdf_with_image.pdf', 
                     'step5_base64_to_pdf.pdf', 'step6_merged.pdf', 'app_signed.pdf']
        for file in test_files:
            if os.path.exists(file):
                size = os.path.getsize(file)
                print(f"- {file} ({size} bytes)")
        
        print("\n请检查这些PDF文件，特别是 app_signed.pdf")
        print("如果这些文件都包含签名，但应用程序中仍然没有签名，")
        print("问题可能在于:")
        print("1. 数据库中的签名数据格式不正确")
        print("2. 应用程序中的文件路径问题")
        print("3. 权限或配置问题")
        
    else:
        print("❌ 基础功能有问题，需要先解决依赖和环境问题")
    
    # 询问是否清理测试文件
    try:
        response = input("\n是否删除测试文件? (y/N): ")
        if response.lower() == 'y':
            cleanup_test_files()
            print("测试文件已清理")
    except:
        pass

if __name__ == "__main__":
    main()
