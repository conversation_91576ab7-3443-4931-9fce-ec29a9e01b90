#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
诊断PDF签名问题
"""

import base64
import io
import os
import sys

def check_dependencies():
    """检查依赖库"""
    print("=== 检查依赖库 ===")
    
    missing_deps = []
    
    try:
        import PyPDF2
        print(f"✅ PyPDF2: {PyPDF2.__version__}")
    except ImportError:
        print("❌ PyPDF2 未安装")
        missing_deps.append("PyPDF2==3.0.1")
    
    try:
        import reportlab
        print(f"✅ ReportLab: {reportlab.Version}")
    except ImportError:
        print("❌ ReportLab 未安装")
        missing_deps.append("reportlab==4.0.4")
    
    try:
        from PIL import Image
        print(f"✅ Pillow: {Image.__version__}")
    except ImportError:
        print("❌ Pillow 未安装")
        missing_deps.append("Pillow==10.1.0")
    
    if missing_deps:
        print(f"\n需要安装的依赖: pip install {' '.join(missing_deps)}")
        return False
    
    return True

def test_basic_pdf_creation():
    """测试基础PDF创建"""
    print("\n=== 测试基础PDF创建 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # 创建简单PDF
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)
        c.drawString(100, 750, "测试PDF文档")
        c.save()
        
        # 保存文件
        with open('test_basic.pdf', 'wb') as f:
            f.write(buffer.getvalue())
        
        print(f"✅ 基础PDF创建成功: test_basic.pdf ({len(buffer.getvalue())} bytes)")
        return True
        
    except Exception as e:
        print(f"❌ 基础PDF创建失败: {e}")
        return False

def test_image_in_pdf():
    """测试在PDF中添加图片"""
    print("\n=== 测试PDF中添加图片 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PIL import Image, ImageDraw
        
        # 创建测试图片
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Test Signature", fill='black')
        draw.line([(20, 60), (180, 60)], fill='black', width=2)
        
        # 保存图片
        img.save('test_signature.png')
        
        # 创建PDF并添加图片
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)
        c.drawString(100, 750, "PDF with Image Test")
        c.drawImage('test_signature.png', 100, 600, width=150, height=75)
        c.save()
        
        # 保存PDF
        with open('test_with_image.pdf', 'wb') as f:
            f.write(buffer.getvalue())
        
        # 清理
        os.remove('test_signature.png')
        
        print(f"✅ PDF图片测试成功: test_with_image.pdf ({len(buffer.getvalue())} bytes)")
        return True
        
    except Exception as e:
        print(f"❌ PDF图片测试失败: {e}")
        return False

def test_base64_to_pdf():
    """测试Base64图片到PDF"""
    print("\n=== 测试Base64图片到PDF ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PIL import Image, ImageDraw
        
        # 创建签名图片
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Base64 Signature", fill='black')
        draw.line([(20, 60), (180, 60)], fill='black', width=2)
        
        # 转换为Base64
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG')
        base64_data = base64.b64encode(img_buffer.getvalue()).decode()
        data_url = f"data:image/png;base64,{base64_data}"
        
        print(f"Base64数据长度: {len(data_url)}")
        
        # 解码Base64
        if data_url.startswith('data:image/'):
            clean_data = data_url.split(',')[1]
        else:
            clean_data = data_url
        
        decoded_data = base64.b64decode(clean_data)
        
        # 保存为临时文件
        with open('temp_base64.png', 'wb') as f:
            f.write(decoded_data)
        
        # 创建PDF
        buffer = io.BytesIO()
        c = canvas.Canvas(buffer, pagesize=A4)
        c.drawString(100, 750, "Base64 to PDF Test")
        c.drawImage('temp_base64.png', 100, 600, width=150, height=75)
        c.save()
        
        # 保存PDF
        with open('test_base64.pdf', 'wb') as f:
            f.write(buffer.getvalue())
        
        # 清理
        os.remove('temp_base64.png')
        
        print(f"✅ Base64到PDF测试成功: test_base64.pdf ({len(buffer.getvalue())} bytes)")
        return True
        
    except Exception as e:
        print(f"❌ Base64到PDF测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_merge():
    """测试PDF合并"""
    print("\n=== 测试PDF合并 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PyPDF2 import PdfReader, PdfWriter
        from PIL import Image, ImageDraw
        
        # 创建原始PDF
        original = io.BytesIO()
        c1 = canvas.Canvas(original, pagesize=A4)
        c1.drawString(100, 750, "原始PDF文档")
        c1.drawString(100, 700, "这里将添加签名")
        c1.save()
        
        # 创建签名覆盖层
        overlay = io.BytesIO()
        c2 = canvas.Canvas(overlay, pagesize=A4)
        
        # 创建签名图片
        img = Image.new('RGB', (200, 100), 'white')
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Merged Signature", fill='black')
        draw.line([(20, 60), (180, 60)], fill='black', width=2)
        img.save('merge_signature.png')
        
        # 添加签名到覆盖层
        page_width, page_height = A4
        c2.drawImage('merge_signature.png', 100, page_height - 300, width=150, height=75)
        c2.save()
        
        # 合并PDF
        original.seek(0)
        overlay.seek(0)
        
        reader = PdfReader(original)
        overlay_reader = PdfReader(overlay)
        writer = PdfWriter()
        
        page = reader.pages[0]
        overlay_page = overlay_reader.pages[0]
        page.merge_page(overlay_page)
        writer.add_page(page)
        
        # 保存结果
        result = io.BytesIO()
        writer.write(result)
        
        with open('test_merged.pdf', 'wb') as f:
            f.write(result.getvalue())
        
        # 清理
        os.remove('merge_signature.png')
        
        print(f"✅ PDF合并测试成功: test_merged.pdf ({len(result.getvalue())} bytes)")
        return True
        
    except Exception as e:
        print(f"❌ PDF合并测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n=== 测试坐标转换 ===")
    
    from reportlab.lib.pagesizes import A4
    
    page_width, page_height = A4
    print(f"PDF页面尺寸: {page_width} x {page_height}")
    
    # 测试不同位置
    test_positions = [
        (100, 100, 150, 75),   # 左上
        (200, 200, 150, 75),   # 中间
        (300, 400, 150, 75),   # 右下
    ]
    
    for i, (x, y, w, h) in enumerate(test_positions):
        # Web坐标转PDF坐标
        pdf_x = x
        pdf_y = page_height - y - h
        
        print(f"位置 {i+1}: Web({x}, {y}) -> PDF({pdf_x}, {pdf_y}), 尺寸({w}, {h})")
        
        # 检查是否在页面内
        if 0 <= pdf_x <= page_width and 0 <= pdf_y <= page_height:
            print(f"  ✅ 位置有效")
        else:
            print(f"  ❌ 位置超出范围")

def main():
    """主函数"""
    print("=== PDF签名问题诊断工具 ===")
    
    # 检查依赖
    if not check_dependencies():
        print("\n请先安装缺失的依赖库")
        return
    
    # 运行测试
    tests = [
        test_basic_pdf_creation,
        test_image_in_pdf,
        test_base64_to_pdf,
        test_pdf_merge,
    ]
    
    results = []
    for test in tests:
        results.append(test())
    
    # 坐标转换测试
    test_coordinate_conversion()
    
    # 总结
    print(f"\n=== 测试结果 ===")
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("✅ 所有基础功能正常，问题可能在应用程序逻辑中")
        print("\n建议检查:")
        print("1. 数据库中的签名数据格式")
        print("2. 签名位置信息是否正确")
        print("3. 应用程序中的坐标转换逻辑")
        print("4. PDF文件路径是否正确")
    else:
        print("❌ 基础功能有问题，需要先解决依赖库问题")
    
    print(f"\n生成的测试文件:")
    test_files = ['test_basic.pdf', 'test_with_image.pdf', 'test_base64.pdf', 'test_merged.pdf']
    for file in test_files:
        if os.path.exists(file):
            print(f"- {file}")

if __name__ == "__main__":
    main()
