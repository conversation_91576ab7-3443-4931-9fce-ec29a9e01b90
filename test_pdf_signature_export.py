#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试PDF签名导出功能
"""

import base64
import io
import os
from PIL import Image, ImageDraw

def test_pdf_libraries():
    """测试PDF处理库是否可用"""
    print("=== 测试PDF处理库 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import letter, A4
        from reportlab.lib.utils import ImageReader
        print("✅ ReportLab 库可用")
    except ImportError as e:
        print(f"❌ ReportLab 库不可用: {e}")
        return False
    
    try:
        from PyPDF2 import PdfReader, PdfWriter
        print("✅ PyPDF2 库可用")
    except ImportError as e:
        print(f"❌ PyPDF2 库不可用: {e}")
        return False
    
    return True

def create_test_signature():
    """创建测试签名"""
    print("\n=== 创建测试签名 ===")
    
    # 创建一个简单的签名图片
    img = Image.new('RGBA', (200, 100), (255, 255, 255, 0))  # 透明背景
    draw = ImageDraw.Draw(img)
    
    # 绘制签名
    draw.text((20, 30), "Test Signature", fill=(0, 0, 0, 255))
    draw.line([(20, 60), (180, 60)], fill=(0, 0, 0, 255), width=2)
    
    # 转换为Base64
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    signature_data = f"data:image/png;base64,{base64.b64encode(buffer.getvalue()).decode()}"
    
    print(f"✅ 测试签名创建成功，数据长度: {len(signature_data)}")
    return signature_data

def create_test_pdf():
    """创建测试PDF文件"""
    print("\n=== 创建测试PDF ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        # 创建测试PDF
        pdf_buffer = io.BytesIO()
        c = canvas.Canvas(pdf_buffer, pagesize=A4)
        
        # 添加一些内容
        c.drawString(100, 750, "这是一个测试PDF文档")
        c.drawString(100, 700, "用于测试签名功能")
        c.drawString(100, 650, "签名将被添加到此文档中")
        
        # 绘制一个框表示签名区域
        c.rect(100, 400, 200, 100, stroke=1, fill=0)
        c.drawString(110, 440, "签名区域")
        
        c.save()
        
        pdf_buffer.seek(0)
        print("✅ 测试PDF创建成功")
        return pdf_buffer
        
    except Exception as e:
        print(f"❌ 创建测试PDF失败: {e}")
        return None

def test_signature_overlay():
    """测试签名覆盖功能"""
    print("\n=== 测试签名覆盖 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.utils import ImageReader
        from PyPDF2 import PdfReader, PdfWriter
        
        # 创建原始PDF
        original_pdf = create_test_pdf()
        if not original_pdf:
            return False
        
        # 创建签名
        signature_data = create_test_signature()
        
        # 解析签名数据
        if signature_data.startswith('data:image/'):
            signature_data = signature_data.split(',')[1]
        
        image_data = base64.b64decode(signature_data)
        image_stream = io.BytesIO(image_data)
        
        # 创建签名覆盖层
        overlay_buffer = io.BytesIO()
        page_width, page_height = A4
        
        c = canvas.Canvas(overlay_buffer, pagesize=A4)
        
        # 在指定位置添加签名
        signature_x = 100
        signature_y = page_height - 500  # PDF坐标系转换
        signature_width = 150
        signature_height = 75
        
        c.drawImage(
            ImageReader(image_stream),
            signature_x, signature_y,
            width=signature_width,
            height=signature_height,
            mask='auto'
        )
        
        c.save()
        
        # 合并PDF
        original_pdf.seek(0)
        overlay_buffer.seek(0)
        
        reader = PdfReader(original_pdf)
        overlay_reader = PdfReader(overlay_buffer)
        writer = PdfWriter()
        
        # 合并第一页
        page = reader.pages[0]
        overlay_page = overlay_reader.pages[0]
        page.merge_page(overlay_page)
        writer.add_page(page)
        
        # 保存结果
        result_buffer = io.BytesIO()
        writer.write(result_buffer)
        result_buffer.seek(0)
        
        print("✅ 签名覆盖测试成功")
        print(f"   原始PDF大小: {len(original_pdf.getvalue())} bytes")
        print(f"   带签名PDF大小: {len(result_buffer.getvalue())} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 签名覆盖测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n=== 测试坐标转换 ===")
    
    from reportlab.lib.pagesizes import A4
    
    page_width, page_height = A4
    print(f"PDF页面尺寸: {page_width} x {page_height}")
    
    # 测试坐标转换
    web_coordinates = [
        (100, 100),  # 左上角
        (200, 200),  # 中间
        (300, 400),  # 右下角
    ]
    
    signature_height = 75
    
    for web_x, web_y in web_coordinates:
        # 转换为PDF坐标（原点在左下角）
        pdf_x = web_x
        pdf_y = page_height - web_y - signature_height
        
        print(f"Web坐标 ({web_x}, {web_y}) -> PDF坐标 ({pdf_x}, {pdf_y})")
    
    print("✅ 坐标转换测试完成")

if __name__ == "__main__":
    print("=== PDF签名导出功能测试 ===")
    
    # 测试PDF库
    if not test_pdf_libraries():
        print("\n❌ PDF库测试失败，请安装必要的库:")
        print("pip install PyPDF2 reportlab")
        exit(1)
    
    # 测试签名创建
    create_test_signature()
    
    # 测试PDF创建
    create_test_pdf()
    
    # 测试坐标转换
    test_coordinate_conversion()
    
    # 测试签名覆盖
    test_signature_overlay()
    
    print("\n=== 测试完成 ===")
    print("如果所有测试都通过，PDF签名导出功能应该可以正常工作。")
