#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试PDF签名功能
"""

import base64
import io
import os
from PIL import Image, ImageDraw

def create_test_signature():
    """创建测试签名"""
    print("创建测试签名...")
    
    # 创建一个简单的签名图片 - 使用RGBA模式支持透明背景
    img = Image.new('RGBA', (200, 100), (255, 255, 255, 0))  # 透明背景
    draw = ImageDraw.Draw(img)
    
    # 绘制签名文字
    draw.text((20, 30), "John Doe", fill=(0, 0, 0, 255))
    draw.line([(20, 60), (180, 60)], fill=(0, 0, 0, 255), width=2)
    
    # 转换为Base64格式（模拟前端发送的数据）
    buffer = io.BytesIO()
    img.save(buffer, format='PNG')
    signature_data = f"data:image/png;base64,{base64.b64encode(buffer.getvalue()).decode()}"
    
    print(f"✅ 签名创建成功，数据长度: {len(signature_data)}")
    return signature_data

def test_reportlab_image():
    """测试ReportLab图片处理"""
    print("\n=== 测试ReportLab图片处理 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.utils import ImageReader
        
        # 创建测试签名
        signature_data = create_test_signature()
        
        # 解析Base64数据
        if signature_data.startswith('data:image/'):
            clean_data = signature_data.split(',')[1]
        else:
            clean_data = signature_data
        
        image_data = base64.b64decode(clean_data)
        print(f"解码后图片数据大小: {len(image_data)} bytes")
        
        # 验证PIL可以读取
        pil_img = Image.open(io.BytesIO(image_data))
        print(f"PIL图像: 尺寸={pil_img.size}, 模式={pil_img.mode}")
        
        # 创建PDF
        pdf_buffer = io.BytesIO()
        page_width, page_height = A4
        c = canvas.Canvas(pdf_buffer, pagesize=A4)
        
        # 添加一些文本
        c.drawString(100, 750, "测试PDF文档")
        c.drawString(100, 700, "签名将显示在下方:")
        
        # 方法1: 直接使用ImageReader
        try:
            image_stream = io.BytesIO(image_data)
            c.drawImage(
                ImageReader(image_stream),
                100, 500,  # 位置
                width=150,
                height=75
            )
            print("✅ 方法1成功: 直接使用ImageReader")
        except Exception as e:
            print(f"❌ 方法1失败: {e}")
            
            # 方法2: 转换为RGB模式
            try:
                if pil_img.mode == 'RGBA':
                    # 创建白色背景
                    rgb_img = Image.new('RGB', pil_img.size, (255, 255, 255))
                    rgb_img.paste(pil_img, mask=pil_img.split()[-1])
                    
                    # 转换回字节流
                    rgb_buffer = io.BytesIO()
                    rgb_img.save(rgb_buffer, format='PNG')
                    
                    c.drawImage(
                        ImageReader(io.BytesIO(rgb_buffer.getvalue())),
                        100, 400,
                        width=150,
                        height=75
                    )
                    print("✅ 方法2成功: 转换为RGB模式")
                else:
                    print("❌ 方法2跳过: 图片不是RGBA模式")
            except Exception as e2:
                print(f"❌ 方法2失败: {e2}")
                
                # 方法3: 保存为临时文件
                try:
                    temp_file = 'temp_signature.png'
                    with open(temp_file, 'wb') as f:
                        f.write(image_data)
                    
                    c.drawImage(
                        temp_file,
                        100, 300,
                        width=150,
                        height=75
                    )
                    
                    # 清理临时文件
                    os.remove(temp_file)
                    print("✅ 方法3成功: 使用临时文件")
                except Exception as e3:
                    print(f"❌ 方法3失败: {e3}")
        
        # 完成PDF
        c.save()
        
        # 保存测试PDF
        with open('test_reportlab.pdf', 'wb') as f:
            f.write(pdf_buffer.getvalue())
        
        print(f"✅ 测试PDF已保存: test_reportlab.pdf")
        print(f"   PDF大小: {len(pdf_buffer.getvalue())} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ ReportLab测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_merge():
    """测试PDF合并"""
    print("\n=== 测试PDF合并 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from reportlab.lib.utils import ImageReader
        from PyPDF2 import PdfReader, PdfWriter
        
        # 创建原始PDF
        original_pdf = io.BytesIO()
        c = canvas.Canvas(original_pdf, pagesize=A4)
        c.drawString(100, 750, "原始PDF文档")
        c.drawString(100, 700, "这里将添加签名")
        c.save()
        
        # 创建签名覆盖层
        signature_data = create_test_signature()
        if signature_data.startswith('data:image/'):
            clean_data = signature_data.split(',')[1]
        else:
            clean_data = signature_data
        
        image_data = base64.b64decode(clean_data)
        
        overlay_pdf = io.BytesIO()
        page_width, page_height = A4
        c2 = canvas.Canvas(overlay_pdf, pagesize=A4)
        
        # 处理图片
        pil_img = Image.open(io.BytesIO(image_data))
        if pil_img.mode == 'RGBA':
            rgb_img = Image.new('RGB', pil_img.size, (255, 255, 255))
            rgb_img.paste(pil_img, mask=pil_img.split()[-1])
            
            rgb_buffer = io.BytesIO()
            rgb_img.save(rgb_buffer, format='PNG')
            image_stream = io.BytesIO(rgb_buffer.getvalue())
        else:
            image_stream = io.BytesIO(image_data)
        
        # 绘制签名
        c2.drawImage(
            ImageReader(image_stream),
            100, page_height - 200,  # 位置
            width=150,
            height=75
        )
        c2.save()
        
        # 合并PDF
        original_pdf.seek(0)
        overlay_pdf.seek(0)
        
        reader = PdfReader(original_pdf)
        overlay_reader = PdfReader(overlay_pdf)
        writer = PdfWriter()
        
        # 合并第一页
        page = reader.pages[0]
        overlay_page = overlay_reader.pages[0]
        page.merge_page(overlay_page)
        writer.add_page(page)
        
        # 保存结果
        result_pdf = io.BytesIO()
        writer.write(result_pdf)
        
        with open('test_merged.pdf', 'wb') as f:
            f.write(result_pdf.getvalue())
        
        print("✅ PDF合并测试成功")
        print(f"   合并后PDF已保存: test_merged.pdf")
        print(f"   文件大小: {len(result_pdf.getvalue())} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ PDF合并测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_coordinate_conversion():
    """测试坐标转换"""
    print("\n=== 测试坐标转换 ===")
    
    from reportlab.lib.pagesizes import A4
    
    page_width, page_height = A4
    print(f"PDF页面尺寸: {page_width} x {page_height}")
    
    # 模拟Web坐标（来自前端）
    web_positions = [
        "100,100,150,75",  # x,y,width,height
        "200,200,150,75",
        "300,400,150,75",
    ]
    
    for i, pos_str in enumerate(web_positions):
        parts = pos_str.split(',')
        if len(parts) >= 4:
            x, y, width, height = map(float, parts)
            
            # 转换为PDF坐标
            pdf_x = x
            pdf_y = page_height - y - height
            
            print(f"位置 {i+1}:")
            print(f"  Web坐标: ({x}, {y})")
            print(f"  PDF坐标: ({pdf_x}, {pdf_y})")
            print(f"  尺寸: {width} x {height}")
            
            # 检查是否在页面范围内
            if 0 <= pdf_x <= page_width and 0 <= pdf_y <= page_height:
                print(f"  ✅ 位置有效")
            else:
                print(f"  ❌ 位置超出页面范围")
            print()

if __name__ == "__main__":
    print("=== PDF签名功能调试 ===")
    
    # 测试坐标转换
    test_coordinate_conversion()
    
    # 测试ReportLab图片处理
    test_reportlab_image()
    
    # 测试PDF合并
    test_pdf_merge()
    
    print("\n=== 调试完成 ===")
    print("请检查生成的PDF文件:")
    print("- test_reportlab.pdf: ReportLab图片测试")
    print("- test_merged.pdf: PDF合并测试")
    print("如果这些文件中包含签名，说明功能正常。")
