<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF手写签名 - {{ file.original_filename }}</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background-color: #f8f9fa;
            margin: 0;
            padding: 0;
            overflow-x: auto;
            overflow-y: auto;
        }

        .container-fluid {
            padding: 10px;
        }
        
        .pdf-container {
            position: relative;
            background-color: #f8f9fa;
            border: 1px solid #ddd;
            border-radius: 8px;
            overflow: visible;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            width: 100%;
            min-height: 100vh;
            height: auto;
        }

        .pdf-viewer {
            width: 100%;
            height: 100vh;
            min-height: 100vh;
            border: none;
            display: block;
            background-color: white;
        }

        .pdf-content-wrapper {
            position: relative;
            width: 100%;
            height: 100vh;
            min-height: 100vh;
            background-color: white;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            margin: 0 auto;
            transform-origin: top left;
        }
        
        .signature-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            min-height: 100vh;
            pointer-events: none;
            z-index: 50;
        }
        
        .signature-item {
            position: absolute;
            cursor: move;
            z-index: 100;
            pointer-events: auto;
            border: 2px dashed transparent;
            padding: 3px;
            border-radius: 6px;
            transition: all 0.2s ease;
            user-select: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
        }

        .signature-item:hover {
            border-color: #007bff;
            background-color: rgba(0, 123, 255, 0.1);
            transform: scale(1.02);
            box-shadow: 0 4px 12px rgba(0, 123, 255, 0.3);
            cursor: grab;
        }

        .signature-item:active,
        .signature-item.dragging {
            opacity: 0.8;
            transform: scale(1.05) rotate(2deg);
            box-shadow: 0 8px 25px rgba(0,0,0,0.3);
            z-index: 1000;
            border-color: #28a745;
            background-color: rgba(40, 167, 69, 0.1);
            cursor: grabbing;
            transition: none;
        }
        
        .signature-image {
            max-width: 200px;
            border: none;
            display: block;
        }
        
        .signature-controls {
            position: absolute;
            top: -45px;
            right: -5px;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 8px;
            padding: 6px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            z-index: 101;
            display: none;
            border: 1px solid #e0e0e0;
            gap: 3px;
            backdrop-filter: blur(5px);
        }

        .signature-item:hover .signature-controls {
            display: flex;
        }

        .signature-controls .btn {
            padding: 6px 10px;
            font-size: 12px;
            border-radius: 4px;
            min-width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            border: 1px solid transparent;
            transition: all 0.2s ease;
        }
        
        .btn-edit {
            background-color: #ffc107;
            border-color: #ffc107;
            color: #212529;
        }
        
        .btn-enlarge {
            background-color: #28a745;
            border-color: #28a745;
            color: white;
        }
        
        .btn-shrink {
            background-color: #17a2b8;
            border-color: #17a2b8;
            color: white;
        }
        
        .btn-delete {
            background-color: #dc3545;
            border-color: #dc3545;
            color: white;
        }
        
        .signature-controls .btn:hover {
            transform: scale(1.1);
            transition: transform 0.1s ease;
        }
        
        .click-overlay {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 5;
            background-color: transparent;
            pointer-events: auto;
            cursor: default;
        }
        
        .click-overlay.waiting-position {
            cursor: crosshair;
        }
        
        .signature-pad-container {
            border: 2px dashed #007bff;
            border-radius: 8px;
            background-color: white;
            padding: 10px;
        }
        
        .signature-pad {
            width: 100%;
            height: 200px;
            border: 1px solid #ddd;
            border-radius: 4px;
            cursor: crosshair;
        }
        
        .toolbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 8px 8px 0 0;
        }
        
        .status-message {
            background-color: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 10px 15px;
            margin: 10px 0;
            border-radius: 0 4px 4px 0;
        }

        .pdf-controls {
            position: absolute;
            top: 10px;
            right: 10px;
            z-index: 100;
            background-color: rgba(255, 255, 255, 0.95);
            border-radius: 6px;
            padding: 8px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.2);
            display: flex;
            gap: 5px;
        }

        .pdf-controls .btn {
            padding: 5px 10px;
            font-size: 12px;
            min-width: auto;
        }

        .zoom-info {
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            position: absolute;
            top: 10px;
            left: 10px;
            z-index: 100;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-3">
        <!-- 页面标题 -->
        <div class="row mb-3">
            <div class="col">
                <div class="d-flex align-items-center">
                    <a href="{{ url_for('main.preview_file', file_id=file.id) }}" class="btn btn-outline-secondary me-3">
                        <i class="fas fa-arrow-left"></i> 返回
                    </a>
                    <h4 class="mb-0">
                        <i class="fas fa-file-pdf text-danger"></i>
                        PDF手写签名 - {{ file.original_filename }}
                    </h4>
                </div>
            </div>
        </div>

        <!-- 工具栏 -->
        <div class="toolbar p-3 mb-3">
            <div class="row align-items-center">
                <div class="col-auto">
                    <button id="btnNewSignature" class="btn btn-light">
                        <i class="fas fa-pen"></i> 新建签名
                    </button>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text bg-light">
                            <i class="fas fa-signature"></i>
                        </span>
                        <select id="selectSignature" class="form-select" style="min-width: 200px;">
                            <option value="">-- 选择已保存的签名 --</option>
                            {% for signature in saved_signatures %}
                            <option value="{{ signature.id }}" data-signature="{{ signature.data }}">
                                {{ signature.date }}
                            </option>
                            {% endfor %}
                        </select>
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text bg-light">笔迹颜色</span>
                        <input type="color" id="penColor" class="form-control form-control-color" value="#000000">
                    </div>
                </div>
                <div class="col-auto">
                    <div class="input-group">
                        <span class="input-group-text bg-light">笔迹粗细</span>
                        <input type="range" id="penSize" class="form-range" min="1" max="10" value="3" style="width: 100px;">
                        <span class="input-group-text bg-light" id="penSizeValue">3px</span>
                    </div>
                </div>
                <div class="col">
                    <div class="status-message">
                        <span class="text-primary" id="statusMessage">
                            <i class="fas fa-info-circle"></i> 
                            点击"新建签名"或选择已保存签名开始
                        </span>
                    </div>
                </div>
            </div>
        </div>

        <!-- PDF容器 -->
        <div class="row">
            <div class="col">
                <div id="pdfContainer" class="pdf-container">
                    <!-- PDF控制按钮 -->
                    <div class="pdf-controls">
                        <button id="zoomOut" class="btn btn-sm btn-outline-secondary" title="缩小">
                            <i class="fas fa-search-minus"></i>
                        </button>
                        <button id="zoomIn" class="btn btn-sm btn-outline-secondary" title="放大">
                            <i class="fas fa-search-plus"></i>
                        </button>
                        <button id="resetZoom" class="btn btn-sm btn-outline-secondary" title="重置缩放">
                            <i class="fas fa-expand-arrows-alt"></i>
                        </button>
                        <button id="fitWidth" class="btn btn-sm btn-outline-secondary" title="适应宽度">
                            <i class="fas fa-arrows-alt-h"></i>
                        </button>
                    </div>

                    <!-- 缩放信息 -->
                    <div id="zoomInfo" class="zoom-info">100%</div>

                    <!-- PDF内容包装器 -->
                    <div id="pdfContentWrapper" class="pdf-content-wrapper">
                        <!-- PDF查看器 -->
                        <iframe id="pdfViewer"
                                src="{{ url_for('main.stream_file', file_id=file.id) }}"
                                class="pdf-viewer">
                        </iframe>

                        <!-- 签名层 -->
                        <div id="signatureLayer" class="signature-layer">
                            <!-- 现有签名会动态添加到这里 -->
                            {% for signature in signatures %}
                            <div class="signature-item"
                                 style="left: {{ signature.position.x if signature.position else 100 }}px;
                                        top: {{ signature.position.y if signature.position else 100 }}px;"
                                 data-signature-id="{{ signature.id }}"
                                 data-original-x="{{ signature.position.x if signature.position else 100 }}"
                                 data-original-y="{{ signature.position.y if signature.position else 100 }}">
                                <img src="{{ signature.signature_data }}"
                                     class="signature-image"
                                     style="width: {{ signature.position.width if signature.position else 150 }}px;">
                                <div class="signature-controls">
                                    <button class="btn btn-sm btn-edit" title="修改签名" onclick="editSignature({{ signature.id }})">
                                        <i class="fas fa-pen"></i>
                                    </button>
                                    <button class="btn btn-sm btn-enlarge" title="放大签名" onclick="resizeSignature({{ signature.id }}, 1.2)">
                                        <i class="fas fa-plus"></i>
                                    </button>
                                    <button class="btn btn-sm btn-shrink" title="缩小签名" onclick="resizeSignature({{ signature.id }}, 0.8)">
                                        <i class="fas fa-minus"></i>
                                    </button>
                                    <button class="btn btn-sm btn-delete" title="删除签名" onclick="deleteSignature({{ signature.id }})">
                                        <i class="fas fa-trash"></i>
                                    </button>
                                </div>
                            </div>
                            {% endfor %}
                        </div>

                        <!-- 点击覆盖层 -->
                        <div id="clickOverlay" class="click-overlay"></div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- 签名模态框 -->
    <div class="modal fade" id="signatureModal" tabindex="-1" aria-hidden="true">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header bg-primary text-white">
                    <h5 class="modal-title">
                        <i class="fas fa-pen"></i> 手写签名
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="text-center mb-3">
                        <p class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            请在下方区域内用鼠标签署您的签名
                        </p>
                    </div>
                    <div class="signature-pad-container">
                        <canvas id="signaturePad" class="signature-pad" width="600" height="200"></canvas>
                    </div>
                    <div class="mt-3">
                        <div class="form-check">
                            <input class="form-check-input" type="checkbox" id="saveAsTemplate">
                            <label class="form-check-label" for="saveAsTemplate">
                                保存为签名模板，以便下次使用
                            </label>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" id="clearPad">
                        <i class="fas fa-eraser"></i> 清除
                    </button>
                    <button type="button" class="btn btn-primary" id="confirmSignature">
                        <i class="fas fa-check"></i> 确认签名
                    </button>
                </div>
            </div>
        </div>
    </div>

    <!-- JavaScript库 -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/signature_pad@4.0.0/dist/signature_pad.umd.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            console.log("=== PDF手写签名功能初始化 ===");
            
            // 获取关键元素
            const pdfContainer = document.getElementById('pdfContainer');
            const pdfContentWrapper = document.getElementById('pdfContentWrapper');
            const pdfViewer = document.getElementById('pdfViewer');
            const signatureLayer = document.getElementById('signatureLayer');
            const clickOverlay = document.getElementById('clickOverlay');
            const btnNewSignature = document.getElementById('btnNewSignature');
            const selectSignature = document.getElementById('selectSignature');
            const statusMessage = document.getElementById('statusMessage');
            const penColor = document.getElementById('penColor');
            const penSize = document.getElementById('penSize');
            const penSizeValue = document.getElementById('penSizeValue');

            // PDF控制元素
            const zoomInBtn = document.getElementById('zoomIn');
            const zoomOutBtn = document.getElementById('zoomOut');
            const resetZoomBtn = document.getElementById('resetZoom');
            const fitWidthBtn = document.getElementById('fitWidth');
            const zoomInfo = document.getElementById('zoomInfo');
            
            // 检查关键元素
            if (!pdfContainer || !signatureLayer || !clickOverlay) {
                console.error("关键元素缺失，无法初始化签名功能");
                alert("页面加载不完整，请刷新页面重试");
                return;
            }
            
            // 全局状态变量
            let signatureMode = 'idle';  // 'idle', 'new', 'saved'
            let waitingForPosition = false;
            let currentSignature = null;
            let signatureItems = [];
            let isDragging = false;
            let dragTarget = null;
            let dragStartX = 0, dragStartY = 0;

            // PDF缩放和滚动状态
            let currentZoom = 1.0;
            let minZoom = 0.5;
            let maxZoom = 3.0;
            let zoomStep = 0.1;
            
            // 初始化签名板
            const canvas = document.getElementById('signaturePad');
            let signaturePad = null;
            let signatureModal = null;
            
            if (canvas && typeof SignaturePad !== 'undefined') {
                signaturePad = new SignaturePad(canvas, {
                    backgroundColor: 'rgb(255, 255, 255)',
                    penColor: 'rgb(0, 0, 0)',
                    minWidth: 2,
                    maxWidth: 5
                });
                console.log("签名板初始化成功");
            } else {
                console.error("签名板初始化失败");
            }
            
            // 初始化模态框
            const modalElement = document.getElementById('signatureModal');
            if (modalElement && typeof bootstrap !== 'undefined') {
                signatureModal = new bootstrap.Modal(modalElement);
                console.log("模态框初始化成功");
            }
            
            // === 事件处理函数 ===

            // 新建签名按钮事件
            if (btnNewSignature) {
                btnNewSignature.onclick = function() {
                    console.log("新建签名按钮被点击");
                    signatureMode = 'new';
                    waitingForPosition = true;

                    // 重置选择框
                    if (selectSignature) {
                        selectSignature.selectedIndex = 0;
                    }

                    // 更新鼠标样式和状态
                    updateCursorStyle();
                    updateStatusMessage('请点击PDF文档上您想要添加签名的位置');
                };
            }

            // 选择已保存签名事件
            if (selectSignature) {
                selectSignature.onchange = function() {
                    const selectedOption = this.options[this.selectedIndex];
                    if (selectedOption.value) {
                        console.log("选择了已保存的签名");
                        signatureMode = 'saved';
                        waitingForPosition = true;

                        updateCursorStyle();
                        updateStatusMessage('请点击PDF文档上您想要放置签名的位置');
                    } else {
                        resetSignatureMode();
                    }
                };
            }

            // 点击覆盖层事件
            clickOverlay.onclick = function(e) {
                console.log("覆盖层被点击", {
                    signatureMode: signatureMode,
                    waitingForPosition: waitingForPosition
                });

                if (!waitingForPosition) {
                    console.log("未在等待位置状态，忽略点击");
                    return;
                }

                // 计算点击位置（考虑缩放和滚动）
                const containerRect = pdfContainer.getBoundingClientRect();
                const wrapperRect = pdfContentWrapper.getBoundingClientRect();

                // 相对于容器的点击位置
                const containerX = e.clientX - containerRect.left;
                const containerY = e.clientY - containerRect.top;

                // 考虑滚动偏移
                const scrollX = pdfContainer.scrollLeft;
                const scrollY = pdfContainer.scrollTop;

                // 考虑缩放，计算在原始文档上的位置
                const x = (containerX + scrollX) / currentZoom;
                const y = (containerY + scrollY) / currentZoom;

                console.log(`点击位置: x=${x}, y=${y}, 缩放: ${currentZoom}, 滚动: ${scrollX},${scrollY}`);

                if (signatureMode === 'new') {
                    // 新建签名模式
                    currentSignature = { x: Math.round(x), y: Math.round(y) };
                    console.log("新建签名模式，显示签名板");

                    // 重置状态
                    waitingForPosition = false;
                    updateCursorStyle();

                    // 显示模态框
                    if (signatureModal) {
                        signatureModal.show();
                    }

                } else if (signatureMode === 'saved') {
                    // 使用已保存签名模式
                    const selectedOption = selectSignature.options[selectSignature.selectedIndex];
                    if (selectedOption && selectedOption.value) {
                        const signatureData = selectedOption.getAttribute('data-signature');
                        console.log("使用已保存签名，数据长度:", signatureData ? signatureData.length : 0);

                        if (signatureData) {
                            addSignatureToDocument(signatureData, x, y);
                            resetSignatureMode();
                        } else {
                            alert('签名数据无效，请重新选择');
                        }
                    }
                }
            };

            // 笔迹粗细调整
            if (penSize && penSizeValue) {
                penSize.oninput = function() {
                    penSizeValue.textContent = this.value + 'px';
                    if (signaturePad) {
                        signaturePad.minWidth = parseInt(this.value) - 1;
                        signaturePad.maxWidth = parseInt(this.value) + 2;
                    }
                };
            }

            // 笔迹颜色调整
            if (penColor) {
                penColor.onchange = function() {
                    if (signaturePad) {
                        signaturePad.penColor = this.value;
                    }
                };
            }

            // 确认签名按钮事件
            const confirmBtn = document.getElementById('confirmSignature');
            if (confirmBtn) {
                confirmBtn.onclick = function() {
                    console.log("确认签名按钮被点击");

                    if (!signaturePad) {
                        alert('签名板未初始化，请刷新页面重试');
                        return;
                    }

                    if (signaturePad.isEmpty()) {
                        alert('请先签名');
                        return;
                    }

                    try {
                        // 获取签名数据
                        const signatureData = signaturePad.toDataURL('image/png');
                        console.log("获取签名数据成功，长度:", signatureData.length);

                        // 确保有位置信息
                        if (!currentSignature || typeof currentSignature.x === 'undefined') {
                            console.warn("位置信息无效，使用默认位置");
                            currentSignature = { x: 100, y: 100 };
                        }

                        // 检查是否保存为模板
                        const saveAsTemplate = document.getElementById('saveAsTemplate').checked;

                        // 保存签名到服务器
                        saveSignatureToServer(signatureData, currentSignature.x, currentSignature.y, saveAsTemplate);

                    } catch (e) {
                        console.error("确认签名过程中出错:", e);
                        alert("创建签名时出错: " + e.message);
                    }
                };
            }

            // 清除签名板
            const clearPadBtn = document.getElementById('clearPad');
            if (clearPadBtn) {
                clearPadBtn.onclick = function() {
                    console.log("清除签名板");
                    if (signaturePad) {
                        signaturePad.clear();
                    }
                };
            }

            // === 工具函数 ===

            // 更新状态消息
            function updateStatusMessage(message) {
                if (statusMessage) {
                    statusMessage.innerHTML = `<i class="fas fa-info-circle"></i> ${message}`;
                }
            }

            // 更新鼠标样式
            function updateCursorStyle() {
                if (clickOverlay) {
                    if (waitingForPosition) {
                        clickOverlay.classList.add('waiting-position');
                    } else {
                        clickOverlay.classList.remove('waiting-position');
                    }
                }
            }

            // 重置签名模式
            function resetSignatureMode() {
                signatureMode = 'idle';
                waitingForPosition = false;
                if (selectSignature) {
                    selectSignature.selectedIndex = 0;
                }
                updateStatusMessage('点击"新建签名"或选择已保存签名开始');
                updateCursorStyle();
            }

            // 保存签名到服务器
            function saveSignatureToServer(signatureData, x, y, saveAsTemplate) {
                console.log("保存签名到服务器", { x, y, saveAsTemplate });

                const formData = new FormData();
                formData.append('signature_data', signatureData);
                formData.append('position_x', x);
                formData.append('position_y', y);
                formData.append('signature_width', '150');
                formData.append('signature_height', '75');
                formData.append('save_as_template', saveAsTemplate);

                fetch(`/file/save_pdf_signature/{{ file.id }}`, {
                    method: 'POST',
                    body: formData
                })
                .then(response => response.json())
                .then(data => {
                    if (data.success) {
                        console.log("签名保存成功");

                        // 添加签名到文档显示
                        addSignatureToDocument(signatureData, x, y, data.signature_id);

                        // 关闭模态框
                        if (signatureModal) {
                            signatureModal.hide();
                        }

                        // 清除签名板
                        if (signaturePad) {
                            signaturePad.clear();
                        }

                        // 重置状态
                        resetSignatureMode();

                        // 显示成功消息
                        if (data.template_saved) {
                            alert('签名保存成功，并已保存为模板！');
                        } else {
                            alert('签名保存成功！');
                        }

                        // 如果保存为模板，刷新页面以更新下拉框
                        if (data.template_saved) {
                            setTimeout(() => {
                                window.location.reload();
                            }, 1000);
                        }

                    } else {
                        console.error("签名保存失败:", data.message);
                        alert('签名保存失败: ' + data.message);
                    }
                })
                .catch(error => {
                    console.error("保存签名时发生错误:", error);
                    alert('保存签名时发生错误，请重试');
                });
            }

            // 添加签名到文档显示
            function addSignatureToDocument(signatureData, x, y, signatureId) {
                console.log(`添加签名到文档: x=${x}, y=${y}, id=${signatureId}`);

                try {
                    // 创建签名容器
                    const signatureDiv = document.createElement('div');
                    signatureDiv.className = 'signature-item';
                    signatureDiv.style.cssText = `
                        position: absolute;
                        left: ${x}px;
                        top: ${y}px;
                        z-index: 100;
                        cursor: move;
                        pointer-events: auto;
                    `;
                    signatureDiv.setAttribute('data-signature-id', signatureId);

                    // 存储原始位置（用于缩放计算）
                    signatureDiv.setAttribute('data-original-x', x);
                    signatureDiv.setAttribute('data-original-y', y);

                    // 创建签名图像
                    const img = document.createElement('img');
                    img.src = signatureData;
                    img.className = 'signature-image';
                    img.style.cssText = `
                        width: 150px;
                        max-height: 100px;
                        border: none;
                        display: block;
                    `;
                    img.draggable = false;

                    signatureDiv.appendChild(img);

                    // 添加控制按钮组
                    const controlsGroup = document.createElement('div');
                    controlsGroup.className = 'signature-controls';

                    // 修改按钮
                    const editBtn = document.createElement('button');
                    editBtn.className = 'btn btn-sm btn-edit';
                    editBtn.innerHTML = '<i class="fas fa-pen"></i>';
                    editBtn.title = '修改签名';
                    editBtn.onclick = function(e) {
                        e.stopPropagation();
                        editSignature(signatureId);
                    };

                    // 放大按钮
                    const enlargeBtn = document.createElement('button');
                    enlargeBtn.className = 'btn btn-sm btn-enlarge';
                    enlargeBtn.innerHTML = '<i class="fas fa-plus"></i>';
                    enlargeBtn.title = '放大签名';
                    enlargeBtn.onclick = function(e) {
                        e.stopPropagation();
                        resizeSignature(signatureId, 1.2);
                    };

                    // 缩小按钮
                    const shrinkBtn = document.createElement('button');
                    shrinkBtn.className = 'btn btn-sm btn-shrink';
                    shrinkBtn.innerHTML = '<i class="fas fa-minus"></i>';
                    shrinkBtn.title = '缩小签名';
                    shrinkBtn.onclick = function(e) {
                        e.stopPropagation();
                        resizeSignature(signatureId, 0.8);
                    };

                    // 删除按钮
                    const deleteBtn = document.createElement('button');
                    deleteBtn.className = 'btn btn-sm btn-delete';
                    deleteBtn.innerHTML = '<i class="fas fa-trash"></i>';
                    deleteBtn.title = '删除签名';
                    deleteBtn.onclick = function(e) {
                        e.stopPropagation();
                        deleteSignature(signatureId);
                    };

                    // 添加按钮到控制组
                    controlsGroup.appendChild(editBtn);
                    controlsGroup.appendChild(enlargeBtn);
                    controlsGroup.appendChild(shrinkBtn);
                    controlsGroup.appendChild(deleteBtn);
                    signatureDiv.appendChild(controlsGroup);

                    // 添加拖拽功能
                    addDragFunctionality(signatureDiv);

                    // 添加到签名层
                    signatureLayer.appendChild(signatureDiv);

                    // 记录签名数据
                    signatureItems.push({
                        element: signatureDiv,
                        data: signatureData,
                        x: x,
                        y: y,
                        id: signatureId
                    });

                    console.log("签名已添加到文档，位置:", x, y);
                } catch (e) {
                    console.error("添加签名到文档时出错:", e);
                    alert("添加签名到文档时出错: " + e.message);
                }
            }

            // === PDF缩放和导航控制 ===

            // 更新缩放显示
            function updateZoomDisplay() {
                const percentage = Math.round(currentZoom * 100);
                if (zoomInfo) {
                    zoomInfo.textContent = percentage + '%';
                }
            }

            // 应用缩放
            function applyZoom(zoom) {
                currentZoom = Math.max(minZoom, Math.min(maxZoom, zoom));

                if (pdfContentWrapper) {
                    pdfContentWrapper.style.transform = `scale(${currentZoom})`;
                    pdfContentWrapper.style.transformOrigin = 'top left';

                    // 计算缩放后的尺寸
                    const originalHeight = window.innerHeight;
                    const scaledHeight = originalHeight * currentZoom;

                    // 更新容器高度以适应缩放
                    pdfContainer.style.height = `${scaledHeight}px`;
                }

                updateZoomDisplay();
                console.log(`PDF缩放已调整到: ${Math.round(currentZoom * 100)}%`);
            }

            // 缩放控制事件
            if (zoomInBtn) {
                zoomInBtn.onclick = function() {
                    applyZoom(currentZoom + zoomStep);
                };
            }

            if (zoomOutBtn) {
                zoomOutBtn.onclick = function() {
                    applyZoom(currentZoom - zoomStep);
                };
            }

            if (resetZoomBtn) {
                resetZoomBtn.onclick = function() {
                    applyZoom(1.0);
                    // 滚动到顶部
                    if (pdfContainer) {
                        pdfContainer.scrollTop = 0;
                        pdfContainer.scrollLeft = 0;
                    }
                };
            }

            if (fitWidthBtn) {
                fitWidthBtn.onclick = function() {
                    if (pdfContainer && pdfContentWrapper) {
                        const containerWidth = pdfContainer.clientWidth;
                        const contentWidth = pdfContentWrapper.offsetWidth;
                        const fitZoom = (containerWidth - 40) / contentWidth; // 留一些边距
                        applyZoom(fitZoom);
                    }
                };
            }

            // 鼠标滚轮缩放
            if (pdfContainer) {
                pdfContainer.addEventListener('wheel', function(e) {
                    if (e.ctrlKey || e.metaKey) {
                        e.preventDefault();

                        const delta = e.deltaY > 0 ? -zoomStep : zoomStep;
                        applyZoom(currentZoom + delta);
                    }
                });
            }

            // 键盘快捷键
            document.addEventListener('keydown', function(e) {
                if (e.ctrlKey || e.metaKey) {
                    switch(e.key) {
                        case '=':
                        case '+':
                            e.preventDefault();
                            applyZoom(currentZoom + zoomStep);
                            break;
                        case '-':
                            e.preventDefault();
                            applyZoom(currentZoom - zoomStep);
                            break;
                        case '0':
                            e.preventDefault();
                            applyZoom(1.0);
                            break;
                    }
                }
            });

            // 初始化缩放显示
            updateZoomDisplay();

            // === 初始化现有签名的拖拽功能 ===
            function initializeExistingSignatures() {
                const existingSignatures = document.querySelectorAll('.signature-item');
                console.log(`找到 ${existingSignatures.length} 个现有签名`);

                existingSignatures.forEach(function(signature, index) {
                    console.log(`初始化签名 ${index + 1} 的拖拽功能`);
                    addDragFunctionality(signature);
                });
            }

            // 页面加载完成后初始化现有签名
            setTimeout(initializeExistingSignatures, 500);

            // === 提示功能 ===
            function showToast(message, type = 'info') {
                // 创建提示元素
                const toast = document.createElement('div');
                toast.className = `alert alert-${type === 'success' ? 'success' : 'info'} position-fixed`;
                toast.style.cssText = `
                    top: 20px;
                    right: 20px;
                    z-index: 9999;
                    min-width: 250px;
                    opacity: 0;
                    transition: opacity 0.3s ease;
                `;
                toast.innerHTML = `
                    <i class="fas fa-${type === 'success' ? 'check-circle' : 'info-circle'}"></i>
                    ${message}
                `;

                document.body.appendChild(toast);

                // 显示动画
                setTimeout(() => {
                    toast.style.opacity = '1';
                }, 100);

                // 自动隐藏
                setTimeout(() => {
                    toast.style.opacity = '0';
                    setTimeout(() => {
                        if (toast.parentNode) {
                            toast.parentNode.removeChild(toast);
                        }
                    }, 300);
                }, 3000);
            }

            // 使showToast全局可用
            window.showToast = showToast;

            console.log("PDF手写签名功能初始化完成");
        });

        // === 全局函数（供HTML调用） ===

        // 编辑签名
        function editSignature(signatureId) {
            console.log("编辑签名:", signatureId);

            // 找到签名元素
            const signatureElement = document.querySelector(`[data-signature-id="${signatureId}"]`);
            if (!signatureElement) {
                alert('找不到要编辑的签名');
                return;
            }

            // 获取当前签名位置
            const currentX = parseInt(signatureElement.getAttribute('data-original-x')) || 0;
            const currentY = parseInt(signatureElement.getAttribute('data-original-y')) || 0;

            // 设置编辑模式
            currentSignature = {
                x: currentX,
                y: currentY,
                element: signatureElement,
                id: signatureId,
                isEditing: true
            };

            console.log(`编辑签名 ID: ${signatureId}, 位置: (${currentX}, ${currentY})`);

            // 清除签名板
            if (signaturePad) {
                signaturePad.clear();
            }

            // 保存原始确认按钮行为
            const confirmBtn = document.getElementById('confirmSignature');
            const originalOnClick = confirmBtn.onclick;

            // 临时修改确认按钮行为
            confirmBtn.onclick = function() {
                if (!signaturePad) {
                    alert('签名板未初始化');
                    return;
                }

                if (signaturePad.isEmpty()) {
                    alert('请先绘制新的签名');
                    return;
                }

                try {
                    // 获取新签名数据
                    const newSignatureData = signaturePad.toDataURL('image/png');

                    // 更新签名图像
                    const img = signatureElement.querySelector('.signature-image');
                    if (img) {
                        img.src = newSignatureData;
                        console.log('签名图像已更新');
                    }

                    // 发送更新请求到服务器
                    updateSignatureOnServer(signatureId, newSignatureData, currentX, currentY);

                    // 关闭模态框
                    if (signatureModal) {
                        signatureModal.hide();
                    }

                    // 恢复原始确认按钮行为
                    confirmBtn.onclick = originalOnClick;

                    // 重置状态
                    currentSignature = null;

                    console.log('签名编辑完成');

                } catch (e) {
                    console.error('编辑签名失败:', e);
                    alert('编辑签名失败: ' + e.message);
                }
            };

            // 显示签名模态框
            if (signatureModal) {
                signatureModal.show();
            }
        }

        // 更新服务器上的签名
        function updateSignatureOnServer(signatureId, signatureData, x, y) {
            const formData = new FormData();
            formData.append('signature_data', signatureData);
            formData.append('position_x', x);
            formData.append('position_y', y);
            formData.append('signature_width', '150');
            formData.append('signature_height', '75');

            fetch(`/file/update_pdf_signature/${signatureId}`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('签名更新成功');
                    // 可以显示成功消息
                } else {
                    console.error('签名更新失败:', data.message);
                    alert('签名更新失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error('更新签名时发生错误:', error);
                alert('更新签名时发生错误，请重试');
            });
        }

        // 调整签名大小
        function resizeSignature(signatureId, scale) {
            console.log("调整签名大小:", signatureId, scale);

            const signatureElement = document.querySelector(`[data-signature-id="${signatureId}"]`);
            if (signatureElement) {
                const img = signatureElement.querySelector('.signature-image');
                if (img) {
                    const currentWidth = parseInt(img.style.width) || 150;
                    const newWidth = Math.min(400, Math.max(50, Math.round(currentWidth * scale)));
                    img.style.width = newWidth + 'px';
                    console.log(`签名大小已调整到: ${newWidth}px`);
                }
            }
        }

        // 删除签名
        function deleteSignature(signatureId) {
            console.log("删除签名:", signatureId);

            if (!confirm('确定要删除这个签名吗？')) {
                return;
            }

            fetch(`/file/delete_pdf_signature/${signatureId}`, {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log("签名删除成功");

                    // 从页面中移除签名元素
                    const signatureElement = document.querySelector(`[data-signature-id="${signatureId}"]`);
                    if (signatureElement) {
                        signatureElement.remove();
                    }

                    alert('签名删除成功！');
                } else {
                    console.error("签名删除失败:", data.message);
                    alert('签名删除失败: ' + data.message);
                }
            })
            .catch(error => {
                console.error("删除签名时发生错误:", error);
                alert('删除签名时发生错误，请重试');
            });
        }

        // 修复后的拖拽功能（支持多个签名同时拖拽）
        function addDragFunctionality(element) {
            const signatureId = element.getAttribute('data-signature-id');
            console.log("为签名添加拖拽功能:", signatureId);

            // 为每个签名元素创建独立的拖拽状态
            let dragState = {
                isDragging: false,
                offsetX: 0,
                offsetY: 0,
                startX: 0,
                startY: 0
            };

            // 鼠标按下事件
            function handleMouseDown(e) {
                // 忽略控制按钮
                if (e.target.closest('.signature-controls') || e.target.closest('.btn')) {
                    return;
                }

                console.log("开始拖拽签名:", signatureId);
                dragState.isDragging = true;

                // 计算鼠标相对于元素的偏移
                const rect = element.getBoundingClientRect();
                dragState.offsetX = e.clientX - rect.left;
                dragState.offsetY = e.clientY - rect.top;
                dragState.startX = e.clientX;
                dragState.startY = e.clientY;

                // 添加拖拽样式
                element.classList.add('dragging');

                // 禁用页面选择
                document.body.style.userSelect = 'none';
                document.body.style.webkitUserSelect = 'none';

                // 将元素置于最前面
                element.style.zIndex = '1000';

                e.preventDefault();
                e.stopPropagation();
            }

            // 鼠标移动事件
            function handleMouseMove(e) {
                if (!dragState.isDragging) return;

                // 获取容器位置 - 修复：直接获取元素而不是依赖外部变量
                const pdfContainer = document.getElementById('pdfContainer');
                const pdfContentWrapper = document.getElementById('pdfContentWrapper');
                const containerRect = pdfContainer.getBoundingClientRect();

                // 获取当前缩放值 - 修复：直接从样式中解析
                let currentZoom = 1.0;
                const transformStyle = pdfContentWrapper.style.transform;
                const scaleMatch = transformStyle.match(/scale\(([^)]+)\)/);
                if (scaleMatch && scaleMatch[1]) {
                    currentZoom = parseFloat(scaleMatch[1]);
                }

                // 计算新位置
                let newX = e.clientX - containerRect.left - dragState.offsetX;
                let newY = e.clientY - containerRect.top - dragState.offsetY;

                // 考虑缩放
                newX = newX / currentZoom;
                newY = newY / currentZoom;

                // 边界限制
                const maxX = pdfContentWrapper.offsetWidth - element.offsetWidth;
                const maxY = pdfContentWrapper.offsetHeight - element.offsetHeight;

                newX = Math.max(0, Math.min(newX, maxX));
                newY = Math.max(0, Math.min(newY, maxY));

                // 应用位置
                element.style.left = newX + 'px';
                element.style.top = newY + 'px';

                // 更新数据属性
                element.setAttribute('data-original-x', newX);
                element.setAttribute('data-original-y', newY);

                e.preventDefault();
            }

            // 鼠标释放事件
            function handleMouseUp(e) {
                if (!dragState.isDragging) return;

                console.log("结束拖拽签名:", signatureId);
                dragState.isDragging = false;

                // 移除拖拽样式
                element.classList.remove('dragging');

                // 恢复页面选择
                document.body.style.userSelect = '';
                document.body.style.webkitUserSelect = '';

                // 恢复z-index
                element.style.zIndex = '100';

                // 保存位置 - 确保获取正确的位置值
                const finalX = parseInt(element.style.left) || 0;
                const finalY = parseInt(element.style.top) || 0;

                console.log(`签名移动完成: ID=${signatureId}, 位置=(${finalX}, ${finalY})`);

                if (signatureId) {
                    saveSignaturePosition(signatureId, finalX, finalY);
                }

                // 显示提示
                showToast('签名位置已更新', 'success');

                e.preventDefault();
            }

            // 触摸事件支持（移动设备）
            function handleTouchStart(e) {
                if (e.touches.length !== 1) return;

                const touch = e.touches[0];
                const mouseEvent = new MouseEvent('mousedown', {
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    bubbles: true,
                    cancelable: true
                });

                handleMouseDown(mouseEvent);
                e.preventDefault(); // 阻止默认行为
            }

            function handleTouchMove(e) {
                if (e.touches.length !== 1 || !dragState.isDragging) return;

                const touch = e.touches[0];
                const mouseEvent = new MouseEvent('mousemove', {
                    clientX: touch.clientX,
                    clientY: touch.clientY,
                    bubbles: true,
                    cancelable: true
                });

                handleMouseMove(mouseEvent);
                e.preventDefault(); // 阻止滚动
            }

            function handleTouchEnd(e) {
                if (!dragState.isDragging) return;

                const mouseEvent = new MouseEvent('mouseup', {
                    bubbles: true,
                    cancelable: true
                });

                handleMouseUp(mouseEvent);
                e.preventDefault(); // 阻止默认行为
            }

            // 绑定事件监听器 - 修复：确保事件绑定到正确的元素上
            element.addEventListener('mousedown', handleMouseDown);
            document.addEventListener('mousemove', handleMouseMove);
            document.addEventListener('mouseup', handleMouseUp);

            // 触摸事件支持 - 修复：确保触摸事件正确绑定
            element.addEventListener('touchstart', handleTouchStart, { passive: false });
            document.addEventListener('touchmove', handleTouchMove, { passive: false });
            document.addEventListener('touchend', handleTouchEnd, { passive: false });

            // 存储清理函数，以便需要时移除事件监听器
            element._dragCleanup = function() {
                element.removeEventListener('mousedown', handleMouseDown);
                document.removeEventListener('mousemove', handleMouseMove);
                document.removeEventListener('mouseup', handleMouseUp);
                element.removeEventListener('touchstart', handleTouchStart);
                element.removeEventListener('touchmove', handleTouchMove);
                element.removeEventListener('touchend', handleTouchEnd);
            };
        }

        // 保存签名位置到服务器
        function saveSignaturePosition(signatureId, x, y) {
            const formData = new FormData();
            formData.append('position_x', x);
            formData.append('position_y', y);

            fetch(`/file/update_signature_position/${signatureId}`, {
                method: 'POST',
                body: formData
            })
            .then(response => response.json())
            .then(data => {
                if (data.success) {
                    console.log('签名位置已保存');
                } else {
                    console.warn('保存签名位置失败:', data.message);
                }
            })
            .catch(error => {
                console.warn('保存签名位置时发生错误:', error);
            });
        }
    </script>
</body>
</html>
