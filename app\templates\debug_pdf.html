<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF签名调试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-bug"></i> PDF签名功能调试</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle"></i>
                            此页面用于调试PDF签名功能。点击下面的按钮测试PDF处理是否正常工作。
                        </div>
                        
                        <div class="mb-4">
                            <h5>测试步骤：</h5>
                            <ol>
                                <li>点击"测试PDF签名功能"按钮</li>
                                <li>下载生成的PDF文件</li>
                                <li>检查PDF中是否包含"Debug Signature"文字和下划线</li>
                                <li>如果看到签名，说明功能正常</li>
                            </ol>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="{{ url_for('main.debug_test_pdf_signature') }}" class="btn btn-primary btn-lg">
                                <i class="fas fa-download"></i> 测试PDF签名功能
                            </a>
                        </div>
                        
                        <div class="mt-4">
                            <h5>预期结果：</h5>
                            <ul>
                                <li>✅ 下载一个名为"debug_signed.pdf"的文件</li>
                                <li>✅ PDF中包含"调试PDF文档"和"测试签名功能"文字</li>
                                <li>✅ PDF中包含"Debug Signature"签名文字和下划线</li>
                            </ul>
                        </div>
                        
                        <div class="mt-4">
                            <h5>如果测试失败：</h5>
                            <ul>
                                <li>❌ 检查服务器日志中的错误信息</li>
                                <li>❌ 确认PyPDF2和ReportLab库已正确安装</li>
                                <li>❌ 检查PIL/Pillow库是否正常工作</li>
                            </ul>
                        </div>
                        
                        <div class="mt-4">
                            <a href="{{ url_for('main.index') }}" class="btn btn-secondary">
                                <i class="fas fa-arrow-left"></i> 返回首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
