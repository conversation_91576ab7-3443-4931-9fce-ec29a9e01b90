# PDF签名导出功能安装指南

## 概述

本指南说明如何安装和配置PDF签名导出功能所需的依赖库。

## 新增依赖

### Python库
- **PyPDF2**: 用于PDF文件的读取和写入
- **ReportLab**: 用于PDF生成和图片嵌入

## 安装步骤

### 1. 安装Python依赖

在项目根目录执行以下命令：

```bash
pip install PyPDF2==3.0.1 reportlab==4.0.4
```

或者使用requirements.txt文件：

```bash
pip install -r requirements.txt
```

### 2. 验证安装

运行测试脚本验证安装是否成功：

```bash
python test_pdf_signature_export.py
```

如果看到以下输出，说明安装成功：
```
✅ ReportLab 库可用
✅ PyPDF2 库可用
✅ 测试签名创建成功
✅ 测试PDF创建成功
✅ 签名覆盖测试成功
```

### 3. 常见问题

#### 问题1: ImportError: No module named 'reportlab'
**解决方案**: 
```bash
pip install reportlab==4.0.4
```

#### 问题2: ImportError: No module named 'PyPDF2'
**解决方案**: 
```bash
pip install PyPDF2==3.0.1
```

#### 问题3: 权限错误
**解决方案**: 
```bash
pip install --user PyPDF2==3.0.1 reportlab==4.0.4
```

#### 问题4: 版本冲突
**解决方案**: 
```bash
pip uninstall PyPDF2 reportlab
pip install PyPDF2==3.0.1 reportlab==4.0.4
```

## 功能测试

### 1. 基本功能测试

1. 启动应用程序
2. 上传一个PDF文件
3. 进入PDF签名页面
4. 添加一个或多个手写签名
5. 点击"导出带签名PDF"按钮
6. 验证下载的PDF文件包含签名

### 2. 高级功能测试

1. 测试多个签名的位置准确性
2. 测试签名的透明度处理
3. 测试大文件PDF的处理性能
4. 测试不同尺寸PDF的兼容性

## 性能优化

### 1. 内存使用
- 大文件PDF处理时注意内存使用
- 及时释放临时对象
- 使用流式处理减少内存占用

### 2. 处理速度
- 签名数量较多时可能需要更长处理时间
- 考虑添加进度提示
- 优化图片处理算法

## 部署注意事项

### 1. 生产环境
- 确保所有依赖库版本一致
- 测试PDF处理的稳定性
- 监控内存和CPU使用情况

### 2. 安全考虑
- 验证上传PDF文件的安全性
- 限制PDF文件大小
- 防止恶意PDF文件攻击

### 3. 备份策略
- 保留原始PDF文件
- 定期备份签名数据
- 记录所有导出操作

## 故障排除

### 1. PDF处理失败
- 检查PDF文件是否损坏
- 验证文件权限
- 查看应用程序日志

### 2. 签名位置不准确
- 检查坐标转换逻辑
- 验证页面尺寸计算
- 测试不同PDF格式

### 3. 内存不足
- 增加系统内存
- 优化PDF处理算法
- 分批处理大文件

## 更新日志

### v1.0.0 (2024-01-15)
- 初始版本
- 支持基本PDF签名导出
- 添加坐标转换功能
- 支持透明背景签名

## 技术支持

如果遇到问题，请：

1. 检查错误日志
2. 运行测试脚本
3. 验证依赖库版本
4. 联系技术支持团队

## 相关文档

- [PDF签名导出功能说明](SIGNATURE_EXPORT_FEATURE_GUIDE.md)
- [手写签名功能指南](README_PDF_SIGNATURE.md)
- [系统部署指南](deployment_guide.md)
