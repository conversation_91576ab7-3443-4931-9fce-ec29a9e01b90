#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的PDF签名测试
"""

import base64
import io
import os
from PIL import Image, ImageDraw

def test_simple_pdf_with_signature():
    """测试简单的PDF签名功能"""
    print("=== 简单PDF签名测试 ===")
    
    try:
        # 检查依赖库
        try:
            from reportlab.pdfgen import canvas
            from reportlab.lib.pagesizes import A4
            print("✅ ReportLab 可用")
        except ImportError:
            print("❌ ReportLab 不可用，请安装: pip install reportlab")
            return False
        
        try:
            from PyPDF2 import PdfReader, PdfWriter
            print("✅ PyPDF2 可用")
        except ImportError:
            print("❌ PyPDF2 不可用，请安装: pip install PyPDF2")
            return False
        
        # 1. 创建原始PDF
        print("\n1. 创建原始PDF...")
        original_pdf = io.BytesIO()
        c = canvas.Canvas(original_pdf, pagesize=A4)
        
        # 添加内容
        c.drawString(100, 750, "这是一个测试PDF文档")
        c.drawString(100, 700, "签名将显示在下方:")
        
        # 绘制签名框
        c.rect(100, 500, 200, 100, stroke=1, fill=0)
        c.drawString(110, 540, "签名区域")
        
        c.save()
        print(f"   原始PDF大小: {len(original_pdf.getvalue())} bytes")
        
        # 2. 创建签名图片
        print("\n2. 创建签名图片...")
        img = Image.new('RGB', (200, 100), (255, 255, 255))  # 白色背景
        draw = ImageDraw.Draw(img)
        
        # 绘制签名
        draw.text((20, 30), "John Doe", fill=(0, 0, 0))
        draw.line([(20, 60), (180, 60)], fill=(0, 0, 0), width=2)
        
        # 保存为临时文件
        temp_signature = 'temp_signature.png'
        img.save(temp_signature)
        print(f"   签名图片已保存: {temp_signature}")
        
        # 3. 创建带签名的PDF
        print("\n3. 创建带签名的PDF...")
        signed_pdf = io.BytesIO()
        c2 = canvas.Canvas(signed_pdf, pagesize=A4)
        
        # 复制原始内容
        c2.drawString(100, 750, "这是一个测试PDF文档")
        c2.drawString(100, 700, "签名将显示在下方:")
        c2.rect(100, 500, 200, 100, stroke=1, fill=0)
        c2.drawString(110, 540, "签名区域")
        
        # 添加签名
        c2.drawImage(temp_signature, 120, 520, width=160, height=60)
        
        c2.save()
        
        # 清理临时文件
        os.remove(temp_signature)
        
        # 4. 保存结果
        print("\n4. 保存结果...")
        with open('simple_signed.pdf', 'wb') as f:
            f.write(signed_pdf.getvalue())
        
        print(f"✅ 简单PDF签名测试成功!")
        print(f"   输出文件: simple_signed.pdf")
        print(f"   文件大小: {len(signed_pdf.getvalue())} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pdf_merge_method():
    """测试PDF合并方法"""
    print("\n=== PDF合并方法测试 ===")
    
    try:
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        from PyPDF2 import PdfReader, PdfWriter
        
        # 1. 创建原始PDF
        original_pdf = io.BytesIO()
        c = canvas.Canvas(original_pdf, pagesize=A4)
        c.drawString(100, 750, "原始PDF文档")
        c.drawString(100, 700, "这里将添加签名")
        c.save()
        
        # 2. 创建签名覆盖层
        overlay_pdf = io.BytesIO()
        c2 = canvas.Canvas(overlay_pdf, pagesize=A4)
        
        # 创建签名图片
        img = Image.new('RGB', (200, 100), (255, 255, 255))
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Overlay Signature", fill=(0, 0, 0))
        draw.line([(20, 60), (180, 60)], fill=(0, 0, 0), width=2)
        
        # 保存为临时文件
        temp_overlay = 'temp_overlay.png'
        img.save(temp_overlay)
        
        # 添加到覆盖层
        page_width, page_height = A4
        c2.drawImage(temp_overlay, 100, page_height - 300, width=160, height=60)
        c2.save()
        
        # 清理临时文件
        os.remove(temp_overlay)
        
        # 3. 合并PDF
        original_pdf.seek(0)
        overlay_pdf.seek(0)
        
        reader = PdfReader(original_pdf)
        overlay_reader = PdfReader(overlay_pdf)
        writer = PdfWriter()
        
        # 合并第一页
        page = reader.pages[0]
        overlay_page = overlay_reader.pages[0]
        page.merge_page(overlay_page)
        writer.add_page(page)
        
        # 4. 保存结果
        result_pdf = io.BytesIO()
        writer.write(result_pdf)
        
        with open('merged_signed.pdf', 'wb') as f:
            f.write(result_pdf.getvalue())
        
        print(f"✅ PDF合并测试成功!")
        print(f"   输出文件: merged_signed.pdf")
        print(f"   文件大小: {len(result_pdf.getvalue())} bytes")
        
        return True
        
    except Exception as e:
        print(f"❌ 合并测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_base64_signature():
    """测试Base64签名处理"""
    print("\n=== Base64签名处理测试 ===")
    
    try:
        # 1. 创建签名图片
        img = Image.new('RGB', (200, 100), (255, 255, 255))
        draw = ImageDraw.Draw(img)
        draw.text((20, 30), "Base64 Test", fill=(0, 0, 0))
        draw.line([(20, 60), (180, 60)], fill=(0, 0, 0), width=2)
        
        # 2. 转换为Base64
        buffer = io.BytesIO()
        img.save(buffer, format='PNG')
        signature_data = f"data:image/png;base64,{base64.b64encode(buffer.getvalue()).decode()}"
        
        print(f"Base64数据长度: {len(signature_data)}")
        
        # 3. 解码Base64
        if signature_data.startswith('data:image/'):
            clean_data = signature_data.split(',')[1]
        else:
            clean_data = signature_data
        
        image_data = base64.b64decode(clean_data)
        print(f"解码后数据长度: {len(image_data)}")
        
        # 4. 验证图片
        decoded_img = Image.open(io.BytesIO(image_data))
        print(f"解码后图片: 尺寸={decoded_img.size}, 模式={decoded_img.mode}")
        
        # 5. 在PDF中使用
        from reportlab.pdfgen import canvas
        from reportlab.lib.pagesizes import A4
        
        pdf_buffer = io.BytesIO()
        c = canvas.Canvas(pdf_buffer, pagesize=A4)
        
        c.drawString(100, 750, "Base64签名测试")
        
        # 保存解码后的图片为临时文件
        temp_file = 'temp_base64.png'
        with open(temp_file, 'wb') as f:
            f.write(image_data)
        
        # 在PDF中绘制
        c.drawImage(temp_file, 100, 600, width=160, height=60)
        c.save()
        
        # 清理临时文件
        os.remove(temp_file)
        
        # 保存结果
        with open('base64_signed.pdf', 'wb') as f:
            f.write(pdf_buffer.getvalue())
        
        print(f"✅ Base64签名测试成功!")
        print(f"   输出文件: base64_signed.pdf")
        
        return True
        
    except Exception as e:
        print(f"❌ Base64测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("=== PDF签名功能测试套件 ===")
    
    # 测试1: 简单PDF签名
    test_simple_pdf_with_signature()
    
    # 测试2: PDF合并方法
    test_pdf_merge_method()
    
    # 测试3: Base64签名处理
    test_base64_signature()
    
    print("\n=== 测试完成 ===")
    print("请检查生成的PDF文件:")
    print("- simple_signed.pdf: 简单方法生成的带签名PDF")
    print("- merged_signed.pdf: 合并方法生成的带签名PDF")
    print("- base64_signed.pdf: Base64处理生成的带签名PDF")
    print("\n如果这些文件都包含签名，说明基础功能正常。")
    print("如果应用程序中的签名仍然不显示，问题可能在于:")
    print("1. 签名数据格式")
    print("2. 坐标转换")
    print("3. 数据库中的签名位置信息")
