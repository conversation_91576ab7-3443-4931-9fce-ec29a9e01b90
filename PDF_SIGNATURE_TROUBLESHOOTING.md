# PDF签名功能故障排除指南

## 问题描述

用户反馈：导出的PDF文件中没有显示签名。

## 可能的原因和解决方案

### 1. 依赖库未安装或版本不兼容

**检查方法：**
```bash
pip list | findstr -i "pypdf2 reportlab pillow"
```

**解决方案：**
```bash
pip install PyPDF2==3.0.1 reportlab==4.0.4 Pillow==10.1.0
```

### 2. 签名数据格式问题

**可能原因：**
- Base64编码格式错误
- 图片数据损坏
- RGBA模式兼容性问题

**检查方法：**
运行测试脚本：
```bash
python debug_pdf_signature.py
```

**解决方案：**
- 确保签名数据以`data:image/png;base64,`开头
- 将RGBA图片转换为RGB格式
- 验证Base64解码是否成功

### 3. 坐标转换问题

**可能原因：**
- Web坐标系与PDF坐标系转换错误
- 签名位置超出页面范围

**检查方法：**
查看日志中的坐标信息：
```
PDF页面尺寸: 595.2 x 841.8
Web坐标: (100, 100) -> PDF坐标: (100, 666.8)
```

**解决方案：**
- 验证坐标转换公式：`pdf_y = page_height - web_y - signature_height`
- 确保转换后的坐标在页面范围内

### 4. ReportLab图片处理问题

**可能原因：**
- ImageReader无法处理某些图片格式
- 透明背景处理失败

**解决方案：**
```python
# 方法1: 转换RGBA为RGB
if pil_img.mode == 'RGBA':
    rgb_img = PILImage.new('RGB', pil_img.size, (255, 255, 255))
    rgb_img.paste(pil_img, mask=pil_img.split()[-1])
    
# 方法2: 使用临时文件
temp_file = 'temp_signature.png'
with open(temp_file, 'wb') as f:
    f.write(image_data)
c.drawImage(temp_file, x, y, width, height)
```

### 5. PDF合并问题

**可能原因：**
- PyPDF2版本兼容性
- PDF页面合并失败

**检查方法：**
```python
# 检查覆盖层是否创建成功
if len(overlay_reader.pages) > 0:
    print("覆盖层创建成功")
else:
    print("覆盖层创建失败")
```

**解决方案：**
- 使用PyPDF2 3.0.1版本
- 确保覆盖层PDF有效
- 检查页面合并过程

## 调试步骤

### 1. 基础环境检查

```bash
# 检查Python版本
python --version

# 检查依赖库
pip list | findstr -i "pypdf2 reportlab pillow"

# 运行基础测试
python test_pdf_merge.py
```

### 2. 应用程序调试

1. 启动应用程序
2. 访问 `/debug/pdf` 页面（需要管理员权限）
3. 点击"测试PDF签名功能"
4. 检查下载的PDF文件

### 3. 日志分析

查看应用程序日志中的相关信息：
```
开始处理PDF文件: /path/to/file.pdf
签名数量: 2
PDF页面尺寸: 595.2 x 841.8
处理签名 1/2
解析位置成功: {'x': 100, 'y': 100, 'width': 150, 'height': 75}
签名图片数据大小: 12345 bytes
PIL图像尺寸: (200, 100), 模式: RGBA
坐标转换: Web(100, 100) -> PDF(100, 666.8)
成功添加签名到PDF: 位置(100, 666.8), 尺寸(150, 75)
```

### 4. 手动测试

创建简单的测试用例：
```python
# 创建测试签名
signature_data = "data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAA..."

# 测试Base64解码
clean_data = signature_data.split(',')[1]
image_data = base64.b64decode(clean_data)

# 测试PIL图片处理
pil_img = Image.open(io.BytesIO(image_data))
print(f"图片尺寸: {pil_img.size}, 模式: {pil_img.mode}")
```

## 常见错误和解决方案

### 错误1: ImportError: No module named 'reportlab'
```bash
pip install reportlab==4.0.4
```

### 错误2: 签名位置不正确
检查坐标转换逻辑：
```python
pdf_y = page_height - web_y - signature_height
```

### 错误3: 图片无法显示
转换图片格式：
```python
if pil_img.mode == 'RGBA':
    rgb_img = PILImage.new('RGB', pil_img.size, (255, 255, 255))
    rgb_img.paste(pil_img, mask=pil_img.split()[-1])
```

### 错误4: PDF合并失败
检查PyPDF2版本：
```bash
pip install PyPDF2==3.0.1
```

## 性能优化建议

### 1. 内存管理
- 及时释放临时对象
- 使用流式处理大文件
- 避免重复创建图片对象

### 2. 处理速度
- 缓存转换后的图片
- 批量处理多个签名
- 优化坐标计算

### 3. 错误处理
- 添加详细的日志记录
- 提供用户友好的错误信息
- 实现降级处理方案

## 测试清单

- [ ] 依赖库安装正确
- [ ] 基础PDF处理功能正常
- [ ] 签名图片可以正确解码
- [ ] 坐标转换计算正确
- [ ] PDF合并功能正常
- [ ] 生成的PDF包含签名
- [ ] 签名位置准确
- [ ] 多个签名都能正确显示

## 联系支持

如果以上步骤都无法解决问题，请提供：

1. 错误日志信息
2. 测试脚本运行结果
3. 系统环境信息（Python版本、操作系统等）
4. 示例PDF文件和签名数据

## 更新记录

- 2024-01-15: 初始版本
- 添加了基础故障排除步骤
- 包含常见错误解决方案
- 提供调试工具和测试方法
